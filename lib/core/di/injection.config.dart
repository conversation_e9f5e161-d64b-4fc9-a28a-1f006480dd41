// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i7;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:praja/common/app_event_bus.dart' as _i5;
import 'package:praja/common/feed_toast/feed_toast_service.dart' as _i52;
import 'package:praja/common/news_feed_tabs_view_model.dart' as _i26;
import 'package:praja/common/users_suggestion/users_suggestion_view_model.dart'
    as _i75;
import 'package:praja/constants/AppConstants.dart' as _i4;
import 'package:praja/features/app_icon_changer/app_icon_changer.dart' as _i41;
import 'package:praja/features/badge_notification/service/badge_notification.dart'
    as _i79;
import 'package:praja/features/bg_downloader/bg_downloader.dart' as _i8;
import 'package:praja/features/circle/join/services/circle_join_graph.dart'
    as _i127;
import 'package:praja/features/circle/join/state/circle_join_view_model.dart'
    as _i128;
import 'package:praja/features/circle/service/circle_service.dart' as _i84;
import 'package:praja/features/creative_carousel/create_poster_cta_tutorial.dart'
    as _i13;
import 'package:praja/features/creative_carousel/creative_carousel_view_model.dart'
    as _i89;
import 'package:praja/features/deeplinks/popup_deeplink_handler.dart' as _i28;
import 'package:praja/features/deeplinks/singular_passthrough_store.dart'
    as _i36;
import 'package:praja/features/direct_messaging/api/api_versioning_interceptor.dart'
    as _i3;
import 'package:praja/features/direct_messaging/api/messaging_api.dart'
    as _i131;
import 'package:praja/features/direct_messaging/database/messaging_store.dart'
    as _i24;
import 'package:praja/features/direct_messaging/platform/chat_notifications_platform.dart'
    as _i10;
import 'package:praja/features/direct_messaging/presentation/chat_details_page/chat_details_page_view_model.dart'
    as _i149;
import 'package:praja/features/direct_messaging/presentation/chat_details_page/chat_members_page_view_model.dart'
    as _i125;
import 'package:praja/features/direct_messaging/presentation/chat_list_page/chat_list_page_view_model.dart'
    as _i150;
import 'package:praja/features/direct_messaging/presentation/chat_page/chat_page_view_model.dart'
    as _i154;
import 'package:praja/features/direct_messaging/presentation/chat_share_page.dart'
    as _i151;
import 'package:praja/features/direct_messaging/presentation/chat_target_picker.dart'
    as _i153;
import 'package:praja/features/direct_messaging/service/message_status_reporter.dart'
    as _i130;
import 'package:praja/features/direct_messaging/service/messaging_config.dart'
    as _i98;
import 'package:praja/features/direct_messaging/service/messaging_service.dart'
    as _i132;
import 'package:praja/features/direct_messaging/service/send_message_delegate.dart'
    as _i114;
import 'package:praja/features/direct_messaging/socket/messaging_socket.dart'
    as _i99;
import 'package:praja/features/direct_messaging/ui/channel_invite_icon.dart'
    as _i9;
import 'package:praja/features/direct_messaging/ui/chat_user_block_dialog.dart'
    as _i152;
import 'package:praja/features/direct_messaging/ui/create_poster_attachment_ui.dart'
    as _i88;
import 'package:praja/features/direct_messaging/ui/link_preview_view_model.dart'
    as _i44;
import 'package:praja/features/direct_messaging/ui/post_preview_view_model.dart'
    as _i106;
import 'package:praja/features/direct_messaging/ui/reply_attachment_ui.dart'
    as _i140;
import 'package:praja/features/direct_messaging/ui/testing_page.dart' as _i143;
import 'package:praja/features/disable_screenshot/disable_screenshot_platform.dart'
    as _i15;
import 'package:praja/features/fan_requests/fan_requests_bottom_sheet_view_model.dart'
    as _i94;
import 'package:praja/features/fan_requests/fan_requests_screen_view_model.dart'
    as _i95;
import 'package:praja/features/fan_requests/service/fan_requests_service.dart'
    as _i51;
import 'package:praja/features/follow_contacts/service/follow_contacts.dart'
    as _i53;
import 'package:praja/features/follow_contacts/view_model/follow_contacts.dart'
    as _i54;
import 'package:praja/features/hashtags/service/hashtags_service.dart' as _i55;
import 'package:praja/features/home/<USER>/home.dart' as _i56;
import 'package:praja/features/image_compressor.dart' as _i19;
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart'
    as _i129;
import 'package:praja/features/localization/localization_view_model.dart'
    as _i22;
import 'package:praja/features/my_feed/my_feed_view_model.dart' as _i100;
import 'package:praja/features/notifications/service/notification.dart'
    as _i102;
import 'package:praja/features/on_boarding/service/auth_service.dart' as _i78;
import 'package:praja/features/payments/service/juspay/juspay.dart' as _i97;
import 'package:praja/features/payments/service/payments_service.dart' as _i27;
import 'package:praja/features/post/create/create_post_api.dart' as _i86;
import 'package:praja/features/post/create/create_post_service.dart' as _i87;
import 'package:praja/features/post/create/create_post_store.dart' as _i12;
import 'package:praja/features/post/create/widgets/post_created_view_model.dart'
    as _i104;
import 'package:praja/features/post/create/widgets/post_creation_status_view_model.dart'
    as _i105;
import 'package:praja/features/posters/poster_carousel_feed_widget/poster_carousel_view_model.dart'
    as _i136;
import 'package:praja/features/posters/poster_photo_update/poster_photo_selection_view_model.dart'
    as _i137;
import 'package:praja/features/posters/poster_photos_history/poster_photos_history_view_model.dart'
    as _i138;
import 'package:praja/features/posters/posters_feed_view/posters_feed_config.dart'
    as _i109;
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_carousel_view_model.dart'
    as _i110;
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_page_view_model.dart'
    as _i60;
import 'package:praja/features/posters/services/poster_carousel_config.dart'
    as _i30;
import 'package:praja/features/posters/services/poster_creative_config.dart'
    as _i107;
import 'package:praja/features/posters/services/poster_photo_update_config.dart'
    as _i108;
import 'package:praja/features/posters/services/poster_preview_store.dart'
    as _i46;
import 'package:praja/features/posters/services/poster_service.dart' as _i59;
import 'package:praja/features/posters/widgets/fan_requests_actions_widget.dart'
    as _i93;
import 'package:praja/features/posters/widgets/premium_help_button.dart'
    as _i63;
import 'package:praja/features/posters/widgets/premium_help_tutorial.dart'
    as _i31;
import 'package:praja/features/posters/widgets/premium_pitch_actions_widget.dart'
    as _i64;
import 'package:praja/features/posters/widgets/upload_poster_creative_page.dart'
    as _i73;
import 'package:praja/features/premium_experience/annual_recharge_pay_wall/annual_recharge_pay_wall_view_model.dart'
    as _i124;
import 'package:praja/features/premium_experience/cancel_membership/cancel_flow_downgrade/cancel_flow_downgrade_sheet_view_model.dart'
    as _i80;
import 'package:praja/features/premium_experience/cancel_membership/cancel_membership_sheet_view_model.dart'
    as _i81;
import 'package:praja/features/premium_experience/cancel_membership/cancellation_confirmation/cancellation_confirmation_sheet_view_model.dart'
    as _i82;
import 'package:praja/features/premium_experience/cancel_membership/premium_benefits_loss_screen/premium_benefits_loss_screen_view_model.dart'
    as _i111;
import 'package:praja/features/premium_experience/downgrade_plan/downgrade_bottom_sheet_view_model.dart'
    as _i92;
import 'package:praja/features/premium_experience/offer_reveal_sheet/offer_reveal_sheet_view_model.dart'
    as _i103;
import 'package:praja/features/premium_experience/payment_bottom_sheet/payment_bottom_sheet_view_model.dart'
    as _i135;
import 'package:praja/features/premium_experience/premium_experience_view_model.dart'
    as _i62;
import 'package:praja/features/premium_experience/premium_success_screen/premium_success_screen_view_model.dart'
    as _i65;
import 'package:praja/features/premium_experience/recharge_pay_wall/recharge_pay_wall_view_model.dart'
    as _i139;
import 'package:praja/features/premium_experience/services/premium_experience_service.dart'
    as _i61;
import 'package:praja/features/premium_experience/subscription_handler.dart'
    as _i115;
import 'package:praja/features/premium_experience/upgrade/upgrade_feed_item_view_model.dart'
    as _i116;
import 'package:praja/features/premium_experience/upgrade/upgrade_view_model.dart'
    as _i117;
import 'package:praja/features/premium_experience/widgets/upi_app_picker.dart'
    as _i118;
import 'package:praja/features/profession_selection/profession_selection_view_model.dart'
    as _i112;
import 'package:praja/features/profession_selection/profession_service.dart'
    as _i66;
import 'package:praja/features/profile/my_profile/my_profile_view_model.dart'
    as _i133;
import 'package:praja/features/profile/widgets/drawer/customer_care_view_model.dart'
    as _i90;
import 'package:praja/features/profile/widgets/navigation_drawer_config.dart'
    as _i101;
import 'package:praja/features/search/common_search/service.dart' as _i85;
import 'package:praja/features/search/search_service.dart' as _i67;
import 'package:praja/features/search/search_view_model.dart' as _i33;
import 'package:praja/features/search/service/circle_search_service.dart'
    as _i83;
import 'package:praja/features/search/service/user_search_service.dart' as _i74;
import 'package:praja/features/search/state/search_view_model.dart' as _i113;
import 'package:praja/features/selection_form/selection_form_widget.dart'
    as _i68;
import 'package:praja/features/share/share_icon_tutorial.dart' as _i35;
import 'package:praja/features/share/ui/share_sheet.dart' as _i142;
import 'package:praja/features/suggestions/service/suggested_list_service.dart'
    as _i69;
import 'package:praja/features/suggestions/view_model/suggested_users_view_model.dart'
    as _i70;
import 'package:praja/features/support_flow/service/support_sheet_service.dart'
    as _i71;
import 'package:praja/features/support_flow/support_sheet_view_model.dart'
    as _i72;
import 'package:praja/features/user/follow/services/user_follow_graph.dart'
    as _i144;
import 'package:praja/features/user/follow/state/user_follow_view_model.dart'
    as _i145;
import 'package:praja/features/verification/get_verified_page.dart' as _i96;
import 'package:praja/features/verification/service/verification_service.dart'
    as _i76;
import 'package:praja/features/verification/verification_config.dart' as _i120;
import 'package:praja/features/video_compressor.dart' as _i38;
import 'package:praja/features/video_posters/video_poster_carousel_view_model.dart'
    as _i147;
import 'package:praja/features/video_posters/video_poster_id_store.dart'
    as _i39;
import 'package:praja/features/video_posters/video_posters_preview_page_view_model.dart'
    as _i40;
import 'package:praja/features/video_posters/video_posters_service.dart'
    as _i121;
import 'package:praja/features/video_posters/video_posters_status_view_model.dart'
    as _i122;
import 'package:praja/features/video_posters/view_model/video_posters_preview_view_model.dart'
    as _i148;
import 'package:praja/mixins/analytics_config.dart' as _i123;
import 'package:praja/network/logging_interceptor.dart' as _i23;
import 'package:praja/network/network_module.dart' as _i155;
import 'package:praja/network/request_headers_interceptor.dart' as _i47;
import 'package:praja/network/token_interceptor.dart' as _i48;
import 'package:praja/network/token_refresh_interceptor.dart' as _i49;
import 'package:praja/presentation/circle_identity_view_model.dart' as _i126;
import 'package:praja/presentation/circle_intro.dart' as _i43;
import 'package:praja/presentation/logged_in_user_view_model.dart' as _i45;
import 'package:praja/presentation/nav_bar_view_model.dart' as _i134;
import 'package:praja/presentation/user_identity_view_model.dart' as _i146;
import 'package:praja/screens/circles/circle_feed_options/view_model/circle_feed_options.dart'
    as _i11;
import 'package:praja/screens/root_view_model.dart' as _i141;
import 'package:praja/services/app_initializer.dart' as _i77;
import 'package:praja/services/app_unread_badge.dart' as _i6;
import 'package:praja/services/circle/circle_identity_store.dart' as _i42;
import 'package:praja/services/device_token_service.dart' as _i91;
import 'package:praja/services/event_flag.dart' as _i16;
import 'package:praja/services/hive.dart' as _i17;
import 'package:praja/services/hive_module.dart' as _i156;
import 'package:praja/services/link/link_preview_store.dart' as _i20;
import 'package:praja/services/link/link_service.dart' as _i21;
import 'package:praja/services/media_upload_service.dart' as _i57;
import 'package:praja/services/navigation.dart' as _i25;
import 'package:praja/services/post/post_preview_store.dart' as _i29;
import 'package:praja/services/post/post_service.dart' as _i58;
import 'package:praja/services/prefs/hive_prefs.dart' as _i18;
import 'package:praja/services/session_store.dart' as _i34;
import 'package:praja/services/user/user_identity_store.dart' as _i50;
import 'package:praja/services/user/user_service.dart' as _i119;
import 'package:praja/services/user_store.dart' as _i37;
import 'package:praja/test/poster_variations_view_model.dart' as _i32;
import 'package:praja/utils/device_id/device_id_platform.dart' as _i14;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i1.GetIt init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final appUnreadBadgeModule = _$AppUnreadBadgeModule();
    final networkModule = _$NetworkModule();
    final hiveModule = _$HiveModule();
    gh.factory<_i3.ApiVersioningInterceptor>(
        () => _i3.ApiVersioningInterceptor());
    gh.lazySingleton<_i4.AppConstants>(() => _i4.AppConstants());
    gh.lazySingleton<_i5.AppEventBus>(() => _i5.AppEventBus());
    gh.lazySingleton<_i6.AppUnreadBadge>(
        () => appUnreadBadgeModule.provideAppUnreadBadgeImpl());
    gh.factory<_i7.BaseOptions>(() => networkModule.baseOptions);
    gh.lazySingleton<_i8.BgDownloader>(() => _i8.BgDownloader());
    gh.factory<_i9.ChannelInviteIconViewModel>(
        () => _i9.ChannelInviteIconViewModel());
    gh.lazySingleton<_i10.ChatNotificationsPlatform>(
        () => _i10.ChatNotificationsPlatform());
    gh.factory<_i11.CircleFeedOptionsViewModel>(
        () => _i11.CircleFeedOptionsViewModel());
    gh.lazySingleton<_i12.CreatePostStore>(() => _i12.CreatePostStore());
    gh.lazySingleton<_i13.CreatePosterCtaTutorial>(
        () => _i13.CreatePosterCtaTutorial());
    gh.lazySingleton<_i14.DeviceIdPlatform>(() => _i14.DeviceIdPlatform());
    gh.lazySingleton<_i7.Dio>(
      () => networkModule.baseHTTPClient(),
      instanceName: 'base',
    );
    gh.lazySingleton<_i15.DisableScreenshotPlatform>(
        () => _i15.DisableScreenshotPlatform());
    gh.lazySingleton<_i16.EventFlags>(() => _i16.EventFlags());
    gh.lazySingleton<_i17.HiveDelegate>(() => _i17.HiveDelegate());
    gh.lazySingleton<_i18.HivePrefs>(
      () => hiveModule.userPrefs(),
      instanceName: 'user_prefs',
    );
    gh.lazySingleton<_i18.HivePrefs>(
      () => hiveModule.appPrefs(),
      instanceName: 'app_prefs',
    );
    gh.factory<_i19.ImageCompressor>(() => _i19.ImageCompressor());
    gh.lazySingleton<_i20.LinkPreviewStore>(
        () => _i20.LinkPreviewStore(gh<_i17.HiveDelegate>()));
    gh.lazySingleton<_i21.LinkService>(
        () => _i21.LinkService(gh<_i20.LinkPreviewStore>()));
    gh.factory<_i22.LocalizationViewModel>(() => _i22.LocalizationViewModel(
        gh<_i18.HivePrefs>(instanceName: 'app_prefs')));
    gh.factory<_i23.LoggingInterceptor>(() => _i23.LoggingInterceptor());
    gh.lazySingleton<_i24.MessagingStore>(() => _i24.MessagingStore());
    gh.lazySingleton<_i25.NavigationService>(() => _i25.NavigationService());
    gh.factory<_i26.NewsFeedTabsViewModel>(() => _i26.NewsFeedTabsViewModel());
    gh.lazySingleton<_i27.PaymentsService>(() => _i27.PaymentsService());
    gh.lazySingleton<_i28.PopupDeeplinkHandler>(
        () => _i28.PopupDeeplinkHandler());
    gh.lazySingleton<_i29.PostPreviewStore>(
        () => _i29.PostPreviewStore(gh<_i17.HiveDelegate>()));
    gh.lazySingleton<_i30.PosterCarouselConfig>(
        () => _i30.PosterCarouselConfig());
    gh.lazySingleton<_i31.PremiumHelpTutorial>(
        () => _i31.PremiumHelpTutorial());
    gh.factory<_i32.PremiumPosterVariationsViewModel>(
        () => _i32.PremiumPosterVariationsViewModel());
    gh.factory<_i33.SearchViewModel>(() => _i33.SearchViewModel());
    gh.lazySingleton<_i34.SessionStore>(() => _i34.SessionStore());
    gh.lazySingleton<_i35.ShareIconTutorial>(() => _i35.ShareIconTutorial());
    gh.lazySingleton<_i36.SingularPassthroughStore>(() =>
        _i36.SingularPassthroughStore(
            gh<_i18.HivePrefs>(instanceName: 'user_prefs')));
    gh.lazySingleton<_i37.UserStore>(() => _i37.UserStore());
    gh.lazySingleton<_i38.VideoCompressor>(() => _i38.VideoCompressor());
    gh.lazySingleton<_i39.VideoPosterIdStore>(() => _i39.VideoPosterIdStore(
        gh<_i18.HivePrefs>(instanceName: 'user_prefs')));
    gh.factory<_i40.VideoPostersPreviewPageViewModel>(
        () => _i40.VideoPostersPreviewPageViewModel());
    gh.lazySingleton<_i41.AppIconChanger>(
        () => _i41.AppIconChanger(gh<_i37.UserStore>()));
    gh.lazySingleton<_i42.CircleIdentityStore>(() => _i42.CircleIdentityStore(
          gh<_i17.HiveDelegate>(),
          gh<_i37.UserStore>(),
        ));
    gh.factory<_i43.CircleIntroViewModel>(() => _i43.CircleIntroViewModel(
        gh<_i18.HivePrefs>(instanceName: 'app_prefs')));
    gh.factory<_i44.LinkPreviewViewModel>(
        () => _i44.LinkPreviewViewModel(gh<_i21.LinkService>()));
    gh.factory<_i45.LoggedInUserViewModel>(
        () => _i45.LoggedInUserViewModel(gh<_i37.UserStore>()));
    gh.lazySingleton<_i46.PosterPreviewStore>(() => _i46.PosterPreviewStore(
          gh<_i17.HiveDelegate>(),
          gh<_i37.UserStore>(),
        ));
    gh.factory<_i47.RequestHeadersInterceptor>(
        () => _i47.RequestHeadersInterceptor(
              gh<_i14.DeviceIdPlatform>(),
              gh<_i34.SessionStore>(),
            ));
    gh.factory<_i48.TokenInterceptor>(() => _i48.TokenInterceptor(
          gh<_i37.UserStore>(),
          gh<_i5.AppEventBus>(),
        ));
    gh.factory<_i49.TokenRefreshInterceptor>(() => _i49.TokenRefreshInterceptor(
          gh<_i7.Dio>(instanceName: 'base'),
          gh<_i37.UserStore>(),
        ));
    gh.lazySingleton<_i50.UserIdentityStore>(() => _i50.UserIdentityStore(
          gh<_i17.HiveDelegate>(),
          gh<_i37.UserStore>(),
        ));
    gh.lazySingleton<_i7.Dio>(
      () => networkModule.messagingHTTPClient(
        gh<_i48.TokenInterceptor>(),
        gh<_i49.TokenRefreshInterceptor>(),
        gh<_i47.RequestHeadersInterceptor>(),
        gh<_i3.ApiVersioningInterceptor>(),
      ),
      instanceName: 'MESSAGING',
    );
    gh.lazySingleton<_i7.Dio>(
      () => networkModule.rorHTTPClient(
        gh<_i48.TokenInterceptor>(),
        gh<_i23.LoggingInterceptor>(),
        gh<_i47.RequestHeadersInterceptor>(),
      ),
      instanceName: 'ROR',
    );
    gh.lazySingleton<_i7.Dio>(
      () => networkModule.nonRorHTTPClient(
        gh<_i48.TokenInterceptor>(),
        gh<_i49.TokenRefreshInterceptor>(),
        gh<_i47.RequestHeadersInterceptor>(),
      ),
      instanceName: 'NON_ROR',
    );
    gh.factory<_i51.FanRequestsService>(
        () => _i51.FanRequestsService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.lazySingleton<_i52.FeedToastService>(
        () => _i52.FeedToastService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i53.FollowContactsService>(
        () => _i53.FollowContactsService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i54.FollowContactsViewModel>(
        () => _i54.FollowContactsViewModel(gh<_i53.FollowContactsService>()));
    gh.factory<_i55.HashtagsService>(
        () => _i55.HashtagsService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i56.HomeService>(
        () => _i56.HomeService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i57.MediaUploadService>(
        () => _i57.MediaUploadService(gh<_i7.Dio>(instanceName: 'NON_ROR')));
    gh.lazySingleton<_i58.PostService>(() => _i58.PostService(
          gh<_i29.PostPreviewStore>(),
          gh<_i7.Dio>(instanceName: 'ROR'),
        ));
    gh.factory<_i59.PosterService>(() => _i59.PosterService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i46.PosterPreviewStore>(),
        ));
    gh.factory<_i60.PostersFeedViewViewModel>(
        () => _i60.PostersFeedViewViewModel(
              gh<_i59.PosterService>(),
              gh<_i18.HivePrefs>(instanceName: 'app_prefs'),
              gh<_i5.AppEventBus>(),
              gh<_i30.PosterCarouselConfig>(),
            ));
    gh.factory<_i61.PremiumExperienceService>(
        () => _i61.PremiumExperienceService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i62.PremiumExperienceViewModel>(
        () => _i62.PremiumExperienceViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i5.AppEventBus>(),
            ));
    gh.factory<_i63.PremiumHelpViewModel>(() => _i63.PremiumHelpViewModel(
          gh<_i31.PremiumHelpTutorial>(),
          gh<_i59.PosterService>(),
        ));
    gh.factory<_i64.PremiumPitchActionsViewModel>(
        () => _i64.PremiumPitchActionsViewModel(gh<_i59.PosterService>()));
    gh.factory<_i65.PremiumSuccessScreenViewModel>(
        () => _i65.PremiumSuccessScreenViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i37.UserStore>(),
              gh<_i5.AppEventBus>(),
            ));
    gh.lazySingleton<_i66.ProfessionService>(
        () => _i66.ProfessionService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i67.SearchService>(
        () => _i67.SearchService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i68.SelectionFormViewModel>(
        () => _i68.SelectionFormViewModel(gh<_i58.PostService>()));
    gh.factory<_i69.SuggestedListService>(
        () => _i69.SuggestedListService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i70.SuggestedUsersViewModel>(
        () => _i70.SuggestedUsersViewModel(gh<_i69.SuggestedListService>()));
    gh.factory<_i71.SupportSheetService>(
        () => _i71.SupportSheetService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i72.SupportSheetViewModel>(
        () => _i72.SupportSheetViewModel(gh<_i71.SupportSheetService>()));
    gh.factory<_i73.UploadPosterCreativeViewModel>(
        () => _i73.UploadPosterCreativeViewModel(gh<_i59.PosterService>()));
    gh.factory<_i74.UserSearchService>(
        () => _i74.UserSearchService(dio: gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i75.UsersSuggestionViewModel>(
        () => _i75.UsersSuggestionViewModel(gh<_i69.SuggestedListService>()));
    gh.factory<_i76.VerificationService>(
        () => _i76.VerificationService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.lazySingleton<_i77.AppInitializer>(
        () => _i77.AppInitializer(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i78.AuthService>(() => _i78.AuthService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i37.UserStore>(),
          gh<_i36.SingularPassthroughStore>(),
        ));
    gh.factory<_i79.BadgeNotificationService>(
        () => _i79.BadgeNotificationService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i80.CancelFlowDowngradeSheetViewModel>(() =>
        _i80.CancelFlowDowngradeSheetViewModel(
            gh<_i61.PremiumExperienceService>()));
    gh.factory<_i81.CancelMembershipSheetViewModel>(
        () => _i81.CancelMembershipSheetViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i5.AppEventBus>(),
            ));
    gh.factory<_i82.CancellationConfirmationSheetViewModel>(() =>
        _i82.CancellationConfirmationSheetViewModel(
            gh<_i61.PremiumExperienceService>()));
    gh.factory<_i83.CircleSearchService>(
        () => _i83.CircleSearchService(dio: gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i84.CircleServiceV2>(() => _i84.CircleServiceV2(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i42.CircleIdentityStore>(),
        ));
    gh.factory<_i85.CommonSearchService>(
        () => _i85.CommonSearchService(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.factory<_i86.CreatePostApi>(
        () => _i86.CreatePostApi(gh<_i7.Dio>(instanceName: 'ROR')));
    gh.lazySingleton<_i87.CreatePostService>(() => _i87.CreatePostService(
          gh<_i86.CreatePostApi>(),
          gh<_i12.CreatePostStore>(),
          gh<_i57.MediaUploadService>(),
          gh<_i38.VideoCompressor>(),
          gh<_i19.ImageCompressor>(),
        ));
    gh.factory<_i88.CreatePosterPreviewViewModel>(
        () => _i88.CreatePosterPreviewViewModel(gh<_i59.PosterService>()));
    gh.factory<_i89.CreativeCarouselViewModel>(
        () => _i89.CreativeCarouselViewModel(
              gh<_i59.PosterService>(),
              gh<_i13.CreatePosterCtaTutorial>(),
            ));
    gh.factory<_i90.CustomerCareViewModel>(
        () => _i90.CustomerCareViewModel(gh<_i77.AppInitializer>()));
    gh.factory<_i91.DeviceTokenService>(() => _i91.DeviceTokenService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i37.UserStore>(),
        ));
    gh.factory<_i92.DowngradeBottomSheetViewModel>(() =>
        _i92.DowngradeBottomSheetViewModel(
            gh<_i61.PremiumExperienceService>()));
    gh.factory<_i93.FanRequestsActionsViewModel>(
        () => _i93.FanRequestsActionsViewModel(gh<_i51.FanRequestsService>()));
    gh.factory<_i94.FanRequestsBottomSheetViewModel>(() =>
        _i94.FanRequestsBottomSheetViewModel(gh<_i51.FanRequestsService>()));
    gh.factory<_i95.FanRequestsScreenViewModel>(
        () => _i95.FanRequestsScreenViewModel(gh<_i51.FanRequestsService>()));
    gh.factory<_i96.GetVerifiedViewModel>(
        () => _i96.GetVerifiedViewModel(gh<_i76.VerificationService>()));
    gh.lazySingleton<_i97.Juspay>(() => _i97.Juspay(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i98.MessagingConfig>(
        () => _i98.MessagingConfig(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i99.MessagingSocket>(
        () => _i99.MessagingSocket(gh<_i98.MessagingConfig>()));
    gh.factory<_i100.MyFeedViewModel>(() => _i100.MyFeedViewModel(
          gh<_i58.PostService>(),
          gh<_i5.AppEventBus>(),
          gh<_i4.AppConstants>(),
          gh<_i30.PosterCarouselConfig>(),
        ));
    gh.lazySingleton<_i101.NavigationDrawerConfig>(
        () => _i101.NavigationDrawerConfig(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i102.NotificationService>(() => _i102.NotificationService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i77.AppInitializer>(),
        ));
    gh.factory<_i103.OfferRevealSheetViewModel>(() =>
        _i103.OfferRevealSheetViewModel(gh<_i61.PremiumExperienceService>()));
    gh.factory<_i104.PostCreatedViewModel>(
        () => _i104.PostCreatedViewModel(gh<_i58.PostService>()));
    gh.factory<_i105.PostCreationStatusViewModel>(
        () => _i105.PostCreationStatusViewModel(
              gh<_i12.CreatePostStore>(),
              gh<_i87.CreatePostService>(),
              gh<_i5.AppEventBus>(),
            ));
    gh.factory<_i106.PostPreviewViewModel>(
        () => _i106.PostPreviewViewModel(gh<_i58.PostService>()));
    gh.lazySingleton<_i107.PosterCreativeConfig>(
        () => _i107.PosterCreativeConfig(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i108.PosterPhotoUpdateConfig>(
        () => _i108.PosterPhotoUpdateConfig(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i109.PostersFeedConfig>(
        () => _i109.PostersFeedConfig(gh<_i77.AppInitializer>()));
    gh.factory<_i110.PostersFeedViewCarouselViewModel>(
        () => _i110.PostersFeedViewCarouselViewModel(
              gh<_i59.PosterService>(),
              gh<_i37.UserStore>(),
              gh<_i18.HivePrefs>(instanceName: 'app_prefs'),
              gh<_i108.PosterPhotoUpdateConfig>(),
              gh<_i4.AppConstants>(),
              gh<_i30.PosterCarouselConfig>(),
            ));
    gh.factory<_i111.PremiumBenefitsLossScreenViewModel>(() =>
        _i111.PremiumBenefitsLossScreenViewModel(
            gh<_i61.PremiumExperienceService>()));
    gh.factory<_i112.ProfessionSelectionViewModel>(
        () => _i112.ProfessionSelectionViewModel(gh<_i66.ProfessionService>()));
    gh.factory<_i113.SearchViewModel>(() => _i113.SearchViewModel(
          gh<_i74.UserSearchService>(),
          gh<_i83.CircleSearchService>(),
        ));
    gh.factory<_i114.SendMessageDelegate>(() => _i114.SendMessageDelegate(
          gh<_i24.MessagingStore>(),
          gh<_i99.MessagingSocket>(),
          gh<_i57.MediaUploadService>(),
          gh<_i19.ImageCompressor>(),
          gh<_i38.VideoCompressor>(),
          gh<_i98.MessagingConfig>(),
          gh<_i37.UserStore>(),
        ));
    gh.factory<_i115.SubscriptionHandler>(() => _i115.SubscriptionHandler(
          gh<_i61.PremiumExperienceService>(),
          gh<_i97.Juspay>(),
        ));
    gh.factory<_i116.UpgradeFeedItemViewModel>(
        () => _i116.UpgradeFeedItemViewModel(gh<_i115.SubscriptionHandler>()));
    gh.factory<_i117.UpgradeViewModel>(() => _i117.UpgradeViewModel(
          gh<_i115.SubscriptionHandler>(),
          gh<_i61.PremiumExperienceService>(),
        ));
    gh.factory<_i118.UpiAppSelectViewModel>(
        () => _i118.UpiAppSelectViewModel(gh<_i97.Juspay>()));
    gh.lazySingleton<_i119.UserService>(() => _i119.UserService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i50.UserIdentityStore>(),
          gh<_i37.UserStore>(),
          gh<_i77.AppInitializer>(),
        ));
    gh.lazySingleton<_i120.VerificationConfig>(
        () => _i120.VerificationConfig(gh<_i77.AppInitializer>()));
    gh.lazySingleton<_i121.VideoPostersService>(() => _i121.VideoPostersService(
          gh<_i7.Dio>(instanceName: 'ROR'),
          gh<_i99.MessagingSocket>(),
          gh<_i8.BgDownloader>(),
          gh<_i39.VideoPosterIdStore>(),
        ));
    gh.factory<_i122.VideoPostersStatusViewModel>(() =>
        _i122.VideoPostersStatusViewModel(gh<_i121.VideoPostersService>()));
    gh.lazySingleton<_i123.AnalyticsConfig>(
        () => _i123.AnalyticsConfig(gh<_i77.AppInitializer>()));
    gh.factory<_i124.AnnualRechargePaywallViewModel>(
        () => _i124.AnnualRechargePaywallViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i115.SubscriptionHandler>(),
            ));
    gh.factory<_i125.ChatMembersPageViewModel>(
        () => _i125.ChatMembersPageViewModel(gh<_i119.UserService>()));
    gh.factory<_i126.CircleIdentityViewModel>(
        () => _i126.CircleIdentityViewModel(gh<_i84.CircleServiceV2>()));
    gh.lazySingleton<_i127.CircleJoinGraph>(() => _i127.CircleJoinGraph(
          circleServiceV2: gh<_i84.CircleServiceV2>(),
          circleIdentityStore: gh<_i42.CircleIdentityStore>(),
        ));
    gh.factory<_i128.CircleJoinViewModel>(() => _i128.CircleJoinViewModel(
        circleJoinGraph: gh<_i127.CircleJoinGraph>()));
    gh.factory<_i129.ImpressionTracker>(() => _i129.ImpressionTracker(
          gh<_i58.PostService>(),
          gh<_i59.PosterService>(),
          gh<_i121.VideoPostersService>(),
        ));
    gh.factory<_i130.MessageStatusReporter>(() => _i130.MessageStatusReporter(
          gh<_i24.MessagingStore>(),
          gh<_i99.MessagingSocket>(),
        ));
    gh.lazySingleton<_i131.MessagingApi>(() => _i131.MessagingApi(
          gh<_i7.Dio>(instanceName: 'MESSAGING'),
          gh<_i98.MessagingConfig>(),
        ));
    gh.lazySingleton<_i132.MessagingService>(() => _i132.MessagingService(
          gh<_i131.MessagingApi>(),
          gh<_i99.MessagingSocket>(),
          gh<_i37.UserStore>(),
          gh<_i24.MessagingStore>(),
          gh<_i119.UserService>(),
          gh<_i84.CircleServiceV2>(),
          gh<_i114.SendMessageDelegate>(),
          gh<_i130.MessageStatusReporter>(),
          gh<_i10.ChatNotificationsPlatform>(),
          gh<_i98.MessagingConfig>(),
        ));
    gh.factory<_i133.MyProfileViewModel>(() => _i133.MyProfileViewModel(
          gh<_i119.UserService>(),
          gh<_i56.HomeService>(),
        ));
    gh.factory<_i134.NavViewModel>(() => _i134.NavViewModel(
          gh<_i102.NotificationService>(),
          gh<_i132.MessagingService>(),
          gh<_i77.AppInitializer>(),
          gh<_i6.AppUnreadBadge>(),
        ));
    gh.factory<_i135.PaymentBottomSheetViewModel>(
        () => _i135.PaymentBottomSheetViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i115.SubscriptionHandler>(),
            ));
    gh.factory<_i136.PosterCarouselViewModel>(
        () => _i136.PosterCarouselViewModel(
              gh<_i59.PosterService>(),
              gh<_i37.UserStore>(),
              gh<_i108.PosterPhotoUpdateConfig>(),
              gh<_i4.AppConstants>(),
              gh<_i30.PosterCarouselConfig>(),
            ));
    gh.factory<_i137.PosterPhotoSelectionViewModel>(
        () => _i137.PosterPhotoSelectionViewModel(
              gh<_i119.UserService>(),
              gh<_i57.MediaUploadService>(),
              gh<_i108.PosterPhotoUpdateConfig>(),
            ));
    gh.factory<_i138.PosterPhotosHistoryViewModel>(
        () => _i138.PosterPhotosHistoryViewModel(gh<_i119.UserService>()));
    gh.factory<_i139.RechargePaywallViewModel>(
        () => _i139.RechargePaywallViewModel(
              gh<_i61.PremiumExperienceService>(),
              gh<_i115.SubscriptionHandler>(),
            ));
    gh.factory<_i140.ReplyViewModel>(
        () => _i140.ReplyViewModel(gh<_i132.MessagingService>()));
    gh.factory<_i141.RootViewModel>(() => _i141.RootViewModel(
          gh<_i37.UserStore>(),
          gh<_i4.AppConstants>(),
          gh<_i119.UserService>(),
          gh<_i34.SessionStore>(),
          gh<_i28.PopupDeeplinkHandler>(),
          gh<_i102.NotificationService>(),
          gh<_i77.AppInitializer>(),
          gh<_i25.NavigationService>(),
          gh<_i5.AppEventBus>(),
        ));
    gh.factory<_i142.ShareViewModel>(
        () => _i142.ShareViewModel(gh<_i132.MessagingService>()));
    gh.factory<_i143.TestingPageViewModel>(
        () => _i143.TestingPageViewModel(gh<_i119.UserService>()));
    gh.lazySingleton<_i144.UserFollowGraph>(
        () => _i144.UserFollowGraph(gh<_i119.UserService>()));
    gh.factory<_i145.UserFollowViewModel>(() => _i145.UserFollowViewModel(
        userFollowGraph: gh<_i144.UserFollowGraph>()));
    gh.factory<_i146.UserIdentityViewModel>(
        () => _i146.UserIdentityViewModel(gh<_i119.UserService>()));
    gh.factory<_i147.VideoPosterCarouselViewModel>(
        () => _i147.VideoPosterCarouselViewModel(
              gh<_i121.VideoPostersService>(),
              gh<_i108.PosterPhotoUpdateConfig>(),
              gh<_i37.UserStore>(),
            ));
    gh.factory<_i148.VideoPostersPreviewViewModel>(() =>
        _i148.VideoPostersPreviewViewModel(gh<_i121.VideoPostersService>()));
    gh.factory<_i149.ChatDetailsViewModel>(() => _i149.ChatDetailsViewModel(
          gh<_i119.UserService>(),
          gh<_i132.MessagingService>(),
        ));
    gh.factory<_i150.ChatListViewModel>(
        () => _i150.ChatListViewModel(gh<_i132.MessagingService>()));
    gh.factory<_i151.ChatShareViewModel>(() => _i151.ChatShareViewModel(
          gh<_i132.MessagingService>(),
          gh<_i98.MessagingConfig>(),
          gh<_i119.UserService>(),
        ));
    gh.factory<_i152.ChatUserBlockViewModel>(() => _i152.ChatUserBlockViewModel(
          gh<_i119.UserService>(),
          gh<_i98.MessagingConfig>(),
          gh<_i132.MessagingService>(),
        ));
    gh.factory<_i153.ChatUserPickerViewModel>(
        () => _i153.ChatUserPickerViewModel(
              gh<_i132.MessagingService>(),
              gh<_i119.UserService>(),
            ));
    gh.factory<_i154.ChatViewModel>(() => _i154.ChatViewModel(
          gh<_i132.MessagingService>(),
          gh<_i24.MessagingStore>(),
          gh<_i37.UserStore>(),
          gh<_i119.UserService>(),
          gh<_i10.ChatNotificationsPlatform>(),
          gh<_i84.CircleServiceV2>(),
          gh<_i127.CircleJoinGraph>(),
        ));
    return this;
  }
}

class _$AppUnreadBadgeModule extends _i6.AppUnreadBadgeModule {}

class _$NetworkModule extends _i155.NetworkModule {}

class _$HiveModule extends _i156.HiveModule {}
