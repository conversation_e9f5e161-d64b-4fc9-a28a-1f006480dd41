import 'dart:io';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/common/take_screenshot.dart';
import 'package:praja/common/widgets/dots_indicator_widget.dart';
import 'package:praja/enums/poster_type.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/disable_screenshot/disable_screenshot.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/layout_feedback_consent_enum.dart';
import 'package:praja/features/posters/models/poster_carousel.dart';
import 'package:praja/features/posters/poster_carousel_feed_widget/poster_carousel_mode.dart';
import 'package:praja/features/posters/poster_carousel_feed_widget/poster_carousel_view_model.dart';
import 'package:praja/features/posters/widgets/custom_gradient_extensions.dart';
import 'package:praja/features/posters/widgets/layout_feedback_widget.dart';
import 'package:praja/features/posters/widgets/poster_carousel_extensions.dart';
import 'package:praja/features/posters/widgets/poster_layout_extensions.dart';
import 'package:praja/features/posters/widgets/poster_layout_share_destination.dart';
import 'package:praja/features/support_flow/models/support_sheet_option.dart';
import 'package:praja/features/support_flow/support_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja_posters/praja_posters.dart';

import 'fan_requests_actions_widget.dart';
import 'poster_banner_widget.dart';
import 'poster_constants.dart';
import 'poster_share_actions_widget.dart';
import 'poster_widget.dart';
import 'premium_help_button.dart';
import 'premium_pitch_actions_widget.dart';
import 'share_poster.dart';

const _posterMargin = 8.0;
const _bannerMargin = 0.0;
const _sponsorBannerHeight = 56.0;
const _premiumPitchBannerHeight = 24.0;

class PosterCarouselWidget extends StatefulWidget {
  final PosterCarousel posterCarousel;
  final String source;
  final PosterCarouselMode mode;
  final bool disableActionButtons;
  final double viewPortFraction;

  const PosterCarouselWidget({
    super.key,
    required this.posterCarousel,
    required this.source,
    this.mode = PosterCarouselMode.dark,
    this.disableActionButtons = false,
    this.viewPortFraction = 0.8,
  });

  @override
  State<PosterCarouselWidget> createState() => _PosterCarouselWidgetState();
}

class _PosterCarouselWidgetState extends State<PosterCarouselWidget>
    with AutomaticKeepAliveClientMixin {
  get isDarkMode => widget.mode == PosterCarouselMode.dark;

  Widget _getActionsWidget(BuildContext context,
      PosterCarouselViewModel viewModel, PosterCarouselState state) {
    final PosterLayout? currentLayout = state.currentLayout();
    final PosterCreative? currentCreative = state.currentCreative();
    if (currentLayout == null || currentCreative == null) {
      return const SizedBox.shrink();
    }

    final premiumPitch = currentLayout.premiumPitch;
    final fanPosterRequest = currentLayout.fanPosterRequest;

    final layoutFeedbackRequest = currentLayout.layoutFeedbackRequest;
    final protocolId = currentLayout.protocolId;

    final currentLayoutLockedCondition =
        currentLayout.isLocked || currentLayout.lockedDeeplink.isNotEmpty;

    if (premiumPitch != null) {
      return Align(
          key: const ValueKey('premium-pitch'),
          alignment: Alignment.bottomCenter,
          child: PremiumPitchActionsWidget(
            premiumPitch: premiumPitch,
            analyticsParams: viewModel.getAnalyticsParams(),
          ));
    } else if (fanPosterRequest != null) {
      return FanRequestsActionsWidget(
        key: const ValueKey('fan-request'),
        fanPosterRequest: fanPosterRequest,
        onFanRequestConsumed: viewModel.goToNextPage,
        analyticsParams: viewModel.getAnalyticsParams(),
      );
    } else {
      return ListenableBuilder(
          listenable: viewModel.layoutFeedbackListener,
          builder: (context, _) {
            return layoutFeedbackRequest != null &&
                    viewModel.isAvailableForLayoutFeedback(protocolId)
                ? AnimatedLayoutFeedbackWidget(
                    key: const ValueKey('layout-feedback-request'),
                    layoutFeedbackRequest: layoutFeedbackRequest,
                    onAccepted: () {
                      viewModel.onLayoutFeedbackConsentReceived(
                        protocolId: protocolId,
                        consent: LayoutFeedbackConsentEnum.accepted,
                      );
                    },
                    onRejected: () async {
                      await context.showSupportSheet(
                          source: 'layout_feedback',
                          onOptionSelected: (option) {
                            if (option != null &&
                                option.type == SupportSheetOptionType.api) {
                              viewModel.onLayoutFeedbackConsentReceived(
                                protocolId: protocolId,
                                consent: LayoutFeedbackConsentEnum.rejected,
                                rejectReason: option.identifier,
                              );
                            }
                          });
                    },
                  )
                : Padding(
                    key: const ValueKey('share-buttons-container'),
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: (state.isCapturing)
                          ? Row(
                              key: const ValueKey('capturing'),
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 14),
                                    child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          const Expanded(child: SizedBox()),
                                          const SizedBox(
                                              width: 18,
                                              height: 18,
                                              child: CircularProgressIndicator(
                                                  strokeWidth: 2)),
                                          const SizedBox(width: 8),
                                          Text(
                                            context.getString(
                                                StringKey.captureLoadingText),
                                            style: TextStyle(
                                              color: isDarkMode
                                                  ? Colors.white
                                                  : const Color(0xff222222),
                                            ),
                                            textScaler:
                                                const TextScaler.linear(1.0),
                                          ),
                                          const Expanded(child: SizedBox()),
                                        ]),
                                  ),
                                )
                              ],
                            )
                          : PosterShareActionsWidget(
                              onShareClicked: () {
                                viewModel.onShareClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            currentCreative.isLocked);
                              },
                              onDownloadClicked: () {
                                viewModel.onDownloadClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            currentCreative.isLocked);
                              },
                              onWhatsappClicked: () {
                                viewModel.onWhatsappClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            currentCreative.isLocked);
                              },
                              minimumActionSize: const Size.fromHeight(40),
                            ),
                    ),
                  );
          });
    }
  }

  Widget _getItem(
    BuildContext context,
    int index, {
    required TakeScreenshotController controller,
    required bool isCapturing,
    required Color? dominantColor,
    required bool showBanner,
    required PosterCarouselViewModel viewModel,
  }) {
    final item = viewModel.state.value.posterCarousel.items[index];
    final layout = item.layout;
    final creative = item.creative;
    return Padding(
      key: ValueKey(index),
      padding: const EdgeInsets.symmetric(horizontal: posterGap / 2),
      child: ViewDetector(
        uniqueId: "poster_carousel_item_$index",
        threshold: 1.0,
        onView: (_) {
          AppAnalytics.onPosterViewed(
            posterType: PosterType.image,
            parameters: {
              'source': 'poster_carousel_${widget.source}',
              'index': index,
              ...(layout.analyticsParams ?? {}),
              ...(creative.analyticsParams ?? {}),
            },
            source: widget.source,
          );
          //if current layout is premium then disappear camera icon
          if (!layout.isBasic) {
            viewModel.disappearCameraIconAfterFewSeconds();
          }
          context.impressionTracker()
            ..trackCreative(creative.id.toString())
            ..trackLayout(layout.id.toString());
        },
        builder: (_, __) => Stack(children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (showBanner)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: _bannerMargin),
                  child: PosterBannerWidget(
                    layout: layout,
                    source: widget.source,
                    showPremiumBanner: layout.isLocked || creative.isLocked,
                  ),
                ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: _posterMargin),
                child: Column(
                  children: [
                    if (item.eventDate.isNotEmpty)
                      Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        width: double.infinity,
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9F1D5),
                        ),
                        child: Text(
                          item.eventDate,
                          textAlign: TextAlign.center,
                          textScaler: const TextScaler.linear(1.0),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF5A3D1B),
                          ),
                        ),
                      ),
                    TakeScreenshot(
                      controller: controller,
                      child: AspectRatio(
                        aspectRatio: posterWidth / posterHeight,
                        child: FittedBox(
                          fit: BoxFit.cover,
                          child: LiveDataBuilder(
                              liveData: viewModel.disappearCameraIcon,
                              builder: (context, disappearCameraIcon) {
                                return PosterWidget(
                                  source: widget.source,
                                  creative: creative,
                                  disappearCameraIcon: disappearCameraIcon,
                                  layout: layout,
                                  capturing: isCapturing,
                                  creativeDominantColor: dominantColor,
                                  onUserPhotoTap: viewModel.onUserPhotoTapped,
                                  onPosterPhotoUpdated: (photo, returnUrl) {
                                    viewModel.onPosterPhotoUpdated(photo);
                                  },
                                );
                              }),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          IgnorePointer(
            child: Container(
              color: layout.fanPosterRequest != null
                  ? isDarkMode
                      ? Colors.grey.shade900.withOpacity(0.5)
                      : Colors.white.withOpacity(0.5)
                  : Colors.transparent,
            ),
          )
        ]),
      ),
    );
  }

  void _onMoreClicked(BuildContext context, PosterCarouselState state) {
    final deeplink = state.posterCarousel.moreDeeplink;
    AppAnalytics.logEvent(name: 'clicked_more_poster_carousel', parameters: {
      'deeplink': deeplink,
    });
    if (deeplink.isNotEmpty) {
      DeeplinkDestination.fromRoute(deeplink)
          ?.go(context, DeeplinkSource.internalDeeplink);
    }
  }

  Widget _getMoreWidget(BuildContext context, PosterCarouselViewModel viewModel,
      PosterCarouselState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: posterGap / 2, vertical: _posterMargin),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: AspectRatio(
          aspectRatio: posterWidth / posterHeight,
          child: InkWell(
            onTap: () {
              _onMoreClicked(context, state);
            },
            child: Stack(
              children: [
                Container(
                    clipBehavior: Clip.hardEdge,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF292929),
                      gradient: state.getGradientForMore()?.toGradient(),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.5),
                          spreadRadius: 2,
                          blurRadius: 2,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                    child: ClipRect(
                      child: ImageFiltered(
                        imageFilter: ImageFilter.blur(
                            sigmaX: 10, sigmaY: 10, tileMode: TileMode.mirror),
                        child: CachedNetworkImage(
                          cacheManager: AppCacheManager.instance,
                          imageUrl: state.bgImageUrlForMore(),
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                    )),
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  margin: const EdgeInsets.all(8),
                  color: Colors.black.withOpacity(0.55),
                ),
                Container(
                  margin: const EdgeInsets.all(8),
                  color: Colors.transparent,
                  child: Center(
                    child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (state.posterCarousel.moreText.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 32.0, vertical: 16.0),
                              child: Text(state.posterCarousel.moreText,
                                  style: const TextStyle(
                                      height: 1.4, fontSize: 15),
                                  textAlign: TextAlign.center),
                            ),
                          const SizedBox(height: 8),
                          TextButton.icon(
                              onPressed: () {
                                _onMoreClicked(context, state);
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.black,
                                backgroundColor: Colors.white,
                                textStyle: const TextStyle(
                                    fontSize: 15, fontWeight: FontWeight.w600),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(24)),
                              ),
                              label: const Padding(
                                padding: EdgeInsets.only(bottom: 2.0),
                                child: Icon(Icons.chevron_right, size: 32),
                              ),
                              icon: Padding(
                                padding: const EdgeInsets.only(left: 12.0),
                                child: Text(
                                  state.posterCarousel.moreCta.isNotEmpty
                                      ? state.posterCarousel.moreCta
                                      : context
                                          .getString(StringKey.seeMoreLabel),
                                ),
                              )),
                        ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _getItems(BuildContext context,
      PosterCarouselViewModel viewModel, PosterCarouselState state) {
    return [
      for (var i = 0; i < state.posterCarousel.items.length; i++)
        _getItem(
          context,
          i,
          controller: viewModel.screenShotControllers[i],
          isCapturing: state.isCapturing && state.currentPage == i,
          showBanner: state.shouldShowBanner(),
          dominantColor:
              state.dominantColors[state.posterCarousel.items[i].creative.url],
          viewModel: viewModel,
        ),
      if (state.hasMore()) _getMoreWidget(context, viewModel, state),
    ];
  }

  Widget _getCarousel(BuildContext context, PosterCarouselViewModel viewModel,
      PosterCarouselState state) {
    bool containsEventDate =
        state.posterCarousel.items.any((item) => item.eventDate.isNotEmpty);
    return CarouselSlider(
      items: _getItems(context, viewModel, state),
      carouselController: viewModel.carouselController,
      options: CarouselOptions(
        height: context.carouselHeight(
          showBanner: state.shouldShowBanner(),
          posterMargin: _posterMargin,
          bannerMargin: _bannerMargin,
          containsEventDate: containsEventDate,
          bannerHeight: state.hasSponsorBanner()
              ? _sponsorBannerHeight
              : state.hasPremiumPitch()
                  ? _premiumPitchBannerHeight
                  : 0,
          viewPortFraction: widget.viewPortFraction,
        ),
        pauseAutoPlayOnTouch: true,
        pauseAutoPlayOnManualNavigate: true,
        enableInfiniteScroll: false,
        enlargeCenterPage: false,
        viewportFraction: widget.viewPortFraction,
        autoPlay: false,
        padEnds: true,
        onPageChanged: (index, reason) {
          final direction = state.currentPage > index ? "left" : "right";
          AppAnalytics.onPosterLayoutSwipe(
              reason: reason.toAnalyticsString(),
              direction: direction,
              parameters: viewModel.getAnalyticsParams());
          Vibrate.feedback(FeedbackType.impact);

          viewModel.onPageChanged(index);
        },
      ),
    );
  }

  Widget _disableScreenshotBasedOnFlag({required Widget child}) {
    if (widget.posterCarousel.disableScreenshot) {
      return DisableScreenshotOnView(
        child: child,
      );
    } else {
      return child;
    }
  }

  Future<void> _logDownloadAfterCompletion(
    PosterLayoutShareDestination destination,
    PosterCarouselViewModel viewModel,
  ) async {
    final params = await viewModel.getShareAnalyticsParams();
    if (destination == PosterLayoutShareDestination.download) {
      AppAnalytics.onPosterDownloadClicked(
        parameters: params,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final viewModel = context.viewModel(widget.posterCarousel, widget.source);
    return EventListener<PosterCarouselEvent>(
      eventQueue: viewModel.eventQueue,
      onEvent: (ctx, event) async {
        if (event is CaptureEvent) {
          try {
            final controller = viewModel.getCurrentScreenshotController();
            final imageResult =
                await controller.captureAsPngBytes(pixelRatio: 4);
            final currentTimeStamp = DateTime.now().millisecondsSinceEpoch;
            File imageFile = await Utils.writeToTempFile(imageResult,
                imageName: "premium_poster_$currentTimeStamp.png");
            viewModel.onCaptureFinished(imageFile.path,
                deeplinkUrl: event.deeplinkUrl);
          } catch (e) {
            viewModel.onCaptureError();
            Utils.showToast(localisedErrorMessage(e));
          }
        } else if (event is ShareEvent) {
          final deeplinkUrl = event.deeplinkUrl;
          try {
            await sharePoster(
              path: event.path,
              destination: event.destination,
              shareText: event.shareText,
              source: widget.source,
              analyticsParams: event.analyticsParams,
            );

            await _logDownloadAfterCompletion(event.destination, viewModel);

            if (deeplinkUrl != null && deeplinkUrl.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (!mounted) return;
                DeeplinkDestination.fromRoute(deeplinkUrl)
                    ?.go(context, DeeplinkSource.internalDeeplink);
              });
            }
          } catch (e) {
            AppAnalytics.logShareOrDownloadFail(
              e.toString(),
              viewModel.state.value.currentLayout()?.id ?? 0,
              isWhatsapp:
                  event.destination == PosterLayoutShareDestination.whatsapp,
              isGenericShare: event.destination ==
                  PosterLayoutShareDestination.externalShare,
              isDownload:
                  event.destination == PosterLayoutShareDestination.download,
              contentType: "poster",
            );
          }
        } else if (event is LockedLayoutEvent) {
          if (event.deeplink.isNotEmpty) {
            DeeplinkDestination.fromRoute(event.deeplink)
                ?.go(context, DeeplinkSource.internalDeeplink);
          }
          viewModel.stopCapturing();
        }
      },
      child: _disableScreenshotBasedOnFlag(
        child: LiveDataBuilder<PosterCarouselState>(
          liveData: viewModel.state,
          builder: (_, state) => Theme(
            data: isDarkMode
                ? ThemeData.dark(useMaterial3: false).copyWith(
                    colorScheme: ColorScheme.fromSwatch(
                        brightness: Brightness.dark,
                        backgroundColor: Colors.grey.shade900,
                        primarySwatch: Styles.circleIndigo),
                    textTheme:
                        ThemeData.dark(useMaterial3: false).textTheme.apply(
                      fontFamily: 'Roboto',
                      fontFamilyFallback: ['NotoSansTelugu'],
                    ))
                : ThemeData.light(useMaterial3: false).copyWith(
                    colorScheme: ColorScheme.fromSwatch(
                        brightness: Brightness.light,
                        backgroundColor: Colors.white,
                        primarySwatch: Styles.circleIndigo),
                    textTheme:
                        ThemeData.light(useMaterial3: false).textTheme.apply(
                      fontFamily: 'Roboto',
                      fontFamilyFallback: ['NotoSansTelugu'],
                    )),
            child: Material(
                color: isDarkMode ? Colors.grey.shade900 : Colors.white,
                child: Column(
                  children: [
                    Container(
                      height: 8,
                      decoration: isDarkMode
                          ? const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.black,
                                  Colors.transparent,
                                ],
                              ),
                            )
                          : null,
                    ),
                    AnimatedSize(
                        duration: const Duration(milliseconds: 200),
                        child: state.showHelp
                            ? Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                    const PremiumHelpButton(
                                      source: "poster_carousel",
                                    ),
                                    SizedBox(
                                        width:
                                            (MediaQuery.of(context).size.width /
                                                    10) +
                                                posterGap / 2),
                                  ])
                            : const SizedBox.shrink()),
                    _getCarousel(context, viewModel, state),
                    if (state.posterCarousel.items.length > 1)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Center(
                          heightFactor: 1.0,
                          child: DotsIndicatorWidget(
                            sizeConfig: const DotsIndicatorSizeConfig(
                                indicatorWidth: 100),
                            length: state.posterCarousel.items.length +
                                (state.hasMore() ? 1 : 0),
                            controller: viewModel.dotsIndicatorController,
                            selectedColor:
                                isDarkMode ? Colors.white : Colors.black,
                            unselectedColor: isDarkMode
                                ? const Color(0xff505050)
                                : Colors.grey.withOpacity(0.4),
                          ),
                        ),
                      ),
                    if (!widget.disableActionButtons)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32.0),
                        child: SizedBox(
                          height: 104,
                          child: Center(
                            child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: _getActionsWidget(
                                  context,
                                  viewModel,
                                  state,
                                )),
                          ),
                        ),
                      ),
                    Container(
                      height: 8,
                      decoration: isDarkMode
                          ? const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black,
                                ],
                              ),
                            )
                          : null,
                    ),
                  ],
                )),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
