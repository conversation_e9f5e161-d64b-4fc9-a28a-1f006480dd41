import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/common/take_screenshot.dart';
import 'package:praja/common/widgets/dots_indicator_widget.dart';
import 'package:praja/enums/poster_type.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/disable_screenshot/disable_screenshot.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/layout_feedback_consent_enum.dart';
import 'package:praja/features/posters/models/posters_feed_view_carousel.dart';
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_carousel_view_model.dart';
import 'package:praja/features/posters/widgets/category_preview_widget.dart';
import 'package:praja/features/posters/widgets/creative_item.dart';
import 'package:praja/features/posters/widgets/fan_requests_actions_widget.dart';
import 'package:praja/features/posters/widgets/layout_feedback_widget.dart';
import 'package:praja/features/posters/widgets/poster_banner_widget.dart';
import 'package:praja/features/posters/widgets/poster_carousel_extensions.dart';
import 'package:praja/features/posters/widgets/poster_constants.dart';
import 'package:praja/features/posters/widgets/poster_layout_extensions.dart';
import 'package:praja/features/posters/widgets/poster_layout_share_destination.dart';
import 'package:praja/features/posters/widgets/poster_share_actions_single_line_widget.dart';
import 'package:praja/features/posters/widgets/poster_widget.dart';
import 'package:praja/features/posters/widgets/premium_pitch_actions_widget.dart';
import 'package:praja/features/posters/widgets/share_poster.dart';
import 'package:praja/features/support_flow/models/support_sheet_option.dart';
import 'package:praja/features/support_flow/support_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja_posters/praja_posters.dart';

const _posterMargin = 8.0;
const _bannerMargin = 0.0;
const _sponsorBannerHeight = 56.0;
const _premiumPitchBannerHeight = 24.0;
const _moreCreativesButtonWidth = 40.0;

class PostersFeedViewCarouselWidget extends StatefulWidget {
  final PostersFeedViewCarousel postersFeedViewCarousel;
  final String source;
  final bool showHorizontalSwipeHint;
  final int pageIndex;
  final Function(String returnUrl) onPhotoUpdated;

  const PostersFeedViewCarouselWidget({
    super.key,
    required this.postersFeedViewCarousel,
    required this.source,
    this.showHorizontalSwipeHint = false,
    required this.pageIndex,
    required this.onPhotoUpdated,
  });

  @override
  State<PostersFeedViewCarouselWidget> createState() =>
      _PostersFeedViewCarouselWidgetState();
}

class _PostersFeedViewCarouselWidgetState
    extends State<PostersFeedViewCarouselWidget>
    with AutomaticKeepAliveClientMixin {
  double get viewPortFraction => 1.0;

  late PostersFeedViewCarouselViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = context.viewModel(
      widget.postersFeedViewCarousel,
      {
        'source': widget.source,
        'page_index': widget.pageIndex,
      },
      showHorizontalSwipeHint: widget.showHorizontalSwipeHint,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _getActionsWidget(
      BuildContext context, PostersFeedViewCarouselState state) {
    final PosterLayout currentLayout = state.currentLayout();
    final PosterCreative currentCreative = state.selectedCreative();

    final premiumPitch = currentLayout.premiumPitch;
    final fanPosterRequest = currentLayout.fanPosterRequest;
    final layoutFeedbackRequest = currentLayout.layoutFeedbackRequest;
    final protocolId = currentLayout.protocolId;
    if (premiumPitch != null) {
      return PremiumPitchActionsWidget(
        key: ValueKey(
            'premium-pitch-${widget.postersFeedViewCarousel.feedItemId}'),
        premiumPitch: premiumPitch,
        analyticsParams: _viewModel.getAnalyticsParams(),
      );
    } else if (fanPosterRequest != null) {
      return FanRequestsActionsWidget(
        key: const ValueKey('fan-request'),
        fanPosterRequest: fanPosterRequest,
        onFanRequestConsumed: _viewModel.goToNextPage,
        analyticsParams: _viewModel.getAnalyticsParams(),
      );
    } else {
      final creativeLockedCondition =
          !currentLayout.isBasic && currentCreative.isLocked;
      final currentLayoutLockedCondition =
          currentLayout.isLocked || currentLayout.lockedDeeplink.isNotEmpty;
      return ListenableBuilder(
          listenable: _viewModel.layoutFeedbackListener,
          builder: (context, _) {
            return layoutFeedbackRequest != null &&
                    _viewModel.isAvailableForLayoutFeedback(protocolId)
                ? AnimatedLayoutFeedbackWidget(
                    key: const ValueKey('layout-feedback-request'),
                    layoutFeedbackRequest: layoutFeedbackRequest,
                    onAccepted: () {
                      _viewModel.onLayoutFeedbackConsentReceived(
                        protocolId: protocolId,
                        consent: LayoutFeedbackConsentEnum.accepted,
                      );
                    },
                    onRejected: () async {
                      await context.showSupportSheet(
                          source: 'layout_feedback',
                          onOptionSelected: (option) {
                            if (option != null &&
                                option.type == SupportSheetOptionType.api) {
                              _viewModel.onLayoutFeedbackConsentReceived(
                                protocolId: protocolId,
                                consent: LayoutFeedbackConsentEnum.rejected,
                                rejectReason: option.identifier,
                              );
                            }
                          });
                    },
                  )
                : Padding(
                    key: const ValueKey('share-buttons-container'),
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: (state.isCapturing)
                          ? Row(
                              key: const ValueKey('capturing'),
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 14),
                                    child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          const Expanded(child: SizedBox()),
                                          const SizedBox(
                                              width: 18,
                                              height: 18,
                                              child: CircularProgressIndicator(
                                                  color: Colors.white,
                                                  strokeWidth: 2)),
                                          const SizedBox(width: 8),
                                          Text(
                                            context.getString(
                                                StringKey.captureLoadingText),
                                            style: const TextStyle(
                                                color: Colors.white),
                                            textScaler:
                                                const TextScaler.linear(1.0),
                                          ),
                                          const Expanded(child: SizedBox()),
                                        ]),
                                  ),
                                )
                              ],
                            )
                          : PosterShareActionsSingleLineWidget(
                              onShareClicked: () {
                                _viewModel.onShareClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            creativeLockedCondition);
                              },
                              onDownloadClicked: () {
                                _viewModel.onDownloadClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            creativeLockedCondition);
                              },
                              onWhatsappClicked: () {
                                _viewModel.onWhatsappClicked(
                                    isLayoutLocked:
                                        currentLayoutLockedCondition ||
                                            creativeLockedCondition);
                              },
                              minimumActionSize: const Size.fromHeight(40),
                            ),
                    ),
                  );
          });
    }
  }

  Widget _getItem(
    BuildContext context,
    PostersFeedViewCarouselState state,
    int index, {
    required TakeScreenshotController controller,
    required bool isCapturing,
    required Color? dominantColor,
    required bool showBanner,
  }) {
    final List<PosterLayout> layouts = state.visibleLayouts();
    final PosterCreative selectedCreative = state.selectedCreative();
    final PosterLayout currentLayout = layouts[index];
    final bool currentCreativeLocked =
        !currentLayout.isBasic && selectedCreative.isLocked;

    return Container(
      key: ValueKey(index),
      padding: const EdgeInsets.symmetric(horizontal: posterGap / 2),
      child: ViewDetector(
        uniqueId: "poster_carousel_item_$index",
        threshold: 1.0,
        onView: (_) {
          AppAnalytics.onPosterViewed(
            posterType: PosterType.image,
            parameters: {
              'index': index,
              'page_index': widget.pageIndex,
              ...(currentLayout.analyticsParams ?? {}),
              ...(selectedCreative.analyticsParams ?? {}),
              ...(state.postersFeedViewCarousel.analyticsParams ?? {}),
            },
            source: widget.source,
          );
          //if current layout is premium then disappear camera icon
          if (!currentLayout.isBasic) {
            _viewModel.disappearCameraIconAfterFewSeconds();
          }
          context.impressionTracker()
            ..trackCreative(selectedCreative.id.toString())
            ..trackLayout(currentLayout.id.toString());
        },
        builder: (_, __) => Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (showBanner)
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: _bannerMargin),
                    child: PosterBannerWidget(
                      layout: currentLayout,
                      source: widget.source,
                      showPremiumBanner:
                          currentLayout.isLocked || currentCreativeLocked,
                    ),
                  ),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: _posterMargin),
                  margin:
                      currentLayout.marginForOverlayIdentitiesForPostersFeed,
                  child: TakeScreenshot(
                    controller: controller,
                    child: AspectRatio(
                      aspectRatio: posterWidth / posterHeight,
                      child: FittedBox(
                        fit: BoxFit.cover,
                        child: Stack(
                          children: [
                            LiveDataBuilder(
                                liveData: _viewModel.disappearCameraIcon,
                                builder: (context, disappearCameraIcon) {
                                  return PosterWidget(
                                    source: widget.source,
                                    disappearCameraIcon: disappearCameraIcon,
                                    onUserPhotoTap:
                                        _viewModel.onUserPhotoTapped,
                                    creative: selectedCreative.paid &&
                                            currentLayout.isBasic
                                        ? state.firstBasicCreative()
                                        : selectedCreative,
                                    layout: currentLayout,
                                    capturing: isCapturing,
                                    creativeDominantColor: selectedCreative
                                                .paid &&
                                            currentLayout.isBasic
                                        ? state
                                            .getFirstBasicCreativeDominantColor()
                                        : dominantColor,
                                    onPosterPhotoUpdated: (photo, returnUrl) {
                                      widget.onPhotoUpdated(returnUrl);
                                    },
                                  );
                                }),
                            Positioned(
                              top: 0,
                              right: 0,
                              left: 0,
                              child: Container(
                                height: posterHeight * 0.65,
                                color: Colors.transparent,
                                child: Row(
                                  children: [
                                    Expanded(
                                        child: InkWell(
                                      onTap: _viewModel.onLeftHalfPosterTap,
                                      child:
                                          Container(color: Colors.transparent),
                                    )),
                                    Expanded(
                                        child: InkWell(
                                      onTap: _viewModel.onRightHalfPosterTap,
                                      child:
                                          Container(color: Colors.transparent),
                                    )),
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            IgnorePointer(
              child: Container(
                color: currentLayout.fanPosterRequest != null
                    ? Colors.black.withOpacity(0.5)
                    : Colors.transparent,
              ),
            )
          ],
        ),
      ),
    );
  }

  List<Widget> _getItems(
      BuildContext context, PostersFeedViewCarouselState state) {
    return [
      for (var i = 0; i < state.visibleLayouts().length; i++)
        _getItem(
          context,
          state,
          i,
          controller: _viewModel.screenShotControllers[i],
          isCapturing: state.isCapturing && state.carouselLayoutIndex == i,
          showBanner: state.shouldShowBanner(),
          dominantColor: state.dominantColors[state.selectedCreative().url],
        ),
    ];
  }

  Widget _getCarousel(
      BuildContext context, PostersFeedViewCarouselState state) {
    return CarouselSlider(
      items: _getItems(context, state),
      carouselController: _viewModel.carouselController,
      options: CarouselOptions(
        height: context.carouselHeight(
            showBanner: state.shouldShowBanner(),
            posterMargin: _posterMargin,
            bannerMargin: _bannerMargin,
            bannerHeight: state.hasSponsorBanner()
                ? _sponsorBannerHeight
                : state.hasPremiumPitch()
                    ? _premiumPitchBannerHeight
                    : 0,
            viewPortFraction: viewPortFraction),
        pauseAutoPlayOnTouch: true,
        pauseAutoPlayOnManualNavigate: true,
        enableInfiniteScroll: false,
        enlargeCenterPage: false,
        viewportFraction: viewPortFraction,
        autoPlay: false,
        padEnds: true,
        onPageChanged: (index, reason) {
          final direction =
              state.carouselLayoutIndex > index ? "left" : "right";
          AppAnalytics.onPosterLayoutSwipe(
              reason: reason.toAnalyticsString(),
              direction: direction,
              parameters: _viewModel.getAnalyticsParams());
          Vibrate.feedback(FeedbackType.impact);

          _viewModel.onPageChanged(index);
        },
      ),
    );
  }

  void _creativeSelectionsSheet(
      BuildContext context, PostersFeedViewCarouselState state) {
    final creatives = state.postersFeedViewCarousel.creatives;
    final sortedCreatives = [
      ...creatives.where((creative) => !creative.paid),
      ...creatives.where((creative) => creative.paid),
    ];
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          color: Colors.black,
          child: Center(
            child: SizedBox(
              height: 170,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: creatives.length,
                itemBuilder: (context, index) {
                  final creativesToUse = state.currentLayout().isBasic
                      ? sortedCreatives
                      : creatives;
                  return CreativeItem(
                    creative: creativesToUse[index],
                    index: index,
                    width: 120,
                    isSelected:
                        state.selectedCreativeId() == creativesToUse[index].id,
                    showDisableUI: state.currentLayout().isBasic,
                    onTap: () {
                      _viewModel.onCreativeSelected(creativesToUse[index].id);
                      AppAnalytics.onPosterCreativeSelected(
                          isPosterTapUX: false,
                          parameters: _viewModel.getAnalyticsParams());
                      Navigator.pop(context);
                    },
                  );
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(width: 16);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _disableScreenshotBasedOnFlag({required Widget child}) {
    if (widget.postersFeedViewCarousel.disableScreenshot) {
      return DisableScreenshotOnView(
        child: child,
      );
    } else {
      return child;
    }
  }

  Widget _moreCreativesButton({required PostersFeedViewCarouselState state}) {
    final imageUrls = state.postersFeedViewCarousel.creatives
        .map((creative) => creative.thumbnailUrl)
        .toList();
    return state.postersFeedViewCarousel.creatives.length <= 1
        ? const SizedBox(
            height: 34,
            width: 34 + 8,
          )
        : InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              AppAnalytics.onSeeMoreCreativesClicked(
                  source: widget.source,
                  params: _viewModel.getAnalyticsParams());
              _creativeSelectionsSheet(context, state);
            },
            child: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: CategoryPreview(
                  images: imageUrls,
                  width: 34,
                )),
          );
  }

  Future<void> _logDownloadAfterCompletion(
      PosterLayoutShareDestination destination) async {
    final params = await _viewModel.getShareAnalyticsParams();
    if (destination == PosterLayoutShareDestination.download) {
      AppAnalytics.onPosterDownloadClicked(
        parameters: params,
        posterType: PosterType.image,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return EventListener<PostersFeedViewCarouselEvent>(
      eventQueue: _viewModel.eventQueue,
      onEvent: (ctx, event) async {
        if (event is CaptureEvent) {
          try {
            final controller = _viewModel.getCurrentScreenshotController();
            final imageResult =
                await controller.captureAsPngBytes(pixelRatio: 4);
            final currentTimeStamp = DateTime.now().millisecondsSinceEpoch;
            File imageFile = await Utils.writeToTempFile(imageResult,
                imageName: "premium_poster_$currentTimeStamp.png");
            _viewModel.onCaptureFinished(imageFile.path,
                deeplinkUrl: event.deeplinkUrl);
          } catch (e) {
            _viewModel.onCaptureError();
            Utils.showToast(localisedErrorMessage(e));
          }
        } else if (event is ShareEvent) {
          final deeplinkUrl = event.deeplinkUrl;
          try {
            await sharePoster(
              path: event.path,
              destination: event.destination,
              shareText: event.shareText,
              source: widget.source,
              analyticsParams: event.analyticsParams,
            );

            // Log share analytics
            await _logDownloadAfterCompletion(event.destination);

            if (deeplinkUrl != null && deeplinkUrl.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (!mounted) return;
                DeeplinkDestination.fromRoute(deeplinkUrl)
                    ?.go(context, DeeplinkSource.internalDeeplink);
              });
            }
          } catch (e) {
            AppAnalytics.logShareOrDownloadFail(
              e.toString(),
              _viewModel.state.value.currentLayout().id,
              isWhatsapp:
                  event.destination == PosterLayoutShareDestination.whatsapp,
              isGenericShare: event.destination ==
                  PosterLayoutShareDestination.externalShare,
              isDownload:
                  event.destination == PosterLayoutShareDestination.download,
              contentType: "poster",
            );
          }
        } else if (event is LockedLayoutEvent) {
          if (event.deeplink.isNotEmpty) {
            DeeplinkDestination.fromRoute(event.deeplink)
                ?.go(context, DeeplinkSource.internalDeeplink);
          }
          _viewModel.stopCapturing();
        }
      },
      child: _disableScreenshotBasedOnFlag(
        child: LiveDataBuilder<PostersFeedViewCarouselState>(
          liveData: _viewModel.state,
          builder: (context, state) => Theme(
            data: ThemeData.dark(useMaterial3: false).copyWith(
                colorScheme: ColorScheme.fromSwatch(
                    brightness: Brightness.dark,
                    backgroundColor: Colors.black,
                    primarySwatch: Styles.circleIndigo),
                textTheme: ThemeData.dark(useMaterial3: false).textTheme.apply(
                  fontFamily: 'Roboto',
                  fontFamilyFallback: ['NotoSansTelugu'],
                )),
            child: Material(
                color: Colors.black,
                child: Column(
                  children: [
                    const Expanded(flex: 2, child: SizedBox()),
                    // Carousel
                    _getCarousel(context, state),
                    // Dots Indicator
                    if (state.visibleLayouts().length > 1 ||
                        state.postersFeedViewCarousel.creatives.length > 1)
                      Row(
                        children: [
                          _moreCreativesButton(state: state),
                          if (state.visibleLayouts().length > 1)
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    right: _moreCreativesButtonWidth),
                                child: Center(
                                  child: DotsIndicatorWidget(
                                    sizeConfig: const DotsIndicatorSizeConfig(
                                      indicatorWidth: 100,
                                    ),
                                    length: state.visibleLayouts().length,
                                    controller:
                                        _viewModel.dotsIndicatorController,
                                    selectedColor: Colors.white,
                                    unselectedColor: const Color(0xff505050),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    const Expanded(flex: 1, child: SizedBox()),
                    // Actions
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: SizedBox(
                        height: 96,
                        child: Center(
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: _getActionsWidget(context, state),
                          ),
                        ),
                      ),
                    ),
                    const Expanded(flex: 3, child: SizedBox()),
                  ],
                )),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
