import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:praja/features/bg_downloader/android_download_path.dart';
import 'package:praja/utils/logger.dart';

Future<void> initializeBgDownloader() async {
  await FlutterDownloader.initialize(debug: kDebugMode);
  await FlutterDownloader.registerCallback(downloadCallback);
}

typedef DownloadTaskListener = Function(String, DownloadTaskStatus, int);

/// Initiate, get / observe downloads in the background
///
/// ## Usage
/// **Start download**:
/// ```dart
/// final bgDownloader = GetIt.I.get<BgDownloader>();
/// final taskId = await startDownload(url);
/// ```
/// No user permission required for storage
/// In Android, it downloads to the public Downloads directory
/// In iOS, it downloads to app storage
///
/// Internally tries to ensure there is only one task for a given URL to download
/// at any given time
///
/// **Observe download status**:
/// ```dart
/// bgDownloader.addListener((taskId, status, progress) {
///   // update progress bar / state
/// });
/// ```
@lazySingleton
class BgDownloader {
  static const defaultSavedDirName = 'bg-downloads';
  final ReceivePort port = ReceivePort();
  final Map<String, List<DownloadTaskListener>> listeners = {};
  final Map<String, Future<BgDownloadTask>> downloadedTasks = {};

  Future<BgDownloadTask?> getDownloadTaskForUrl(String url) async {
    final tasks = (await FlutterDownloader.loadTasksWithRawQuery(
            query: "SELECT * from task WHERE url = '$url'")) ??
        [];
    if (tasks.isEmpty) return null;
    if (tasks.length > 1) {
      logNonFatal("Multiple download tasks for the same url: $url",
          Exception("${tasks.length} tasks for the same url: $url"));
    }
    return BgDownloadTask._fromDownloadTask(tasks.first);
  }

  Future<BgDownloadTask> getDownloadTaskForTaskId(String taskId) async {
    final tasks = await FlutterDownloader.loadTasksWithRawQuery(
            query: "SELECT * from task WHERE task_id = '$taskId'") ??
        [];
    if (tasks.isEmpty) {
      throw ArgumentError.value(
          taskId, "No download task found for task ID: $taskId");
    }
    return BgDownloadTask._fromDownloadTask(tasks.first);
  }

  Future<bool> _checkIfFileExists(String url) async {
    if (url.isEmpty) {
      return false;
    }
    final file = File(url);
    return await file.exists();
  }

  /// Enqueue a task to download [url] in the background
  /// If a task already exists for the same [url], then we re-use it if we can
  /// otherwise remove the old task and create a new one
  ///
  /// Send [deleteOldDownload] as `true` if you are aware that you can't
  /// use the previous task's result. We will remove the previous task
  /// and it's associated downloaded file if exists
  Future<BgDownloadTask> _startDownload(String url,
      {bool deleteOldDownload = false,
      String prefix = "Praja-Video-Poster-"}) async {
    final task = await getDownloadTaskForUrl(url);
    if (task != null) {
      final bool taskFileExists = await _checkIfFileExists(task.downloadPath);
      if (task.status == DownloadTaskStatus.paused) {
        await FlutterDownloader.resume(taskId: task.taskId);
        return task;
      } else if ([
        DownloadTaskStatus.failed,
        DownloadTaskStatus.canceled,
        DownloadTaskStatus.undefined,
      ].contains(task.status)) {
        await FlutterDownloader.remove(taskId: task.taskId);
        // continue and initiate a new task
      } else if (deleteOldDownload &&
          task.status == DownloadTaskStatus.complete) {
        await FlutterDownloader.remove(taskId: task.taskId);
        // remove the previous downloaded file
        final file = File(task.downloadPath);
        if (await file.exists()) {
          try {
            await file.delete();
          } on FileSystemException catch (_) {
            // ignore
          }
        }
        // continue and initiate a new task
      } else if (task.status == DownloadTaskStatus.complete &&
          !taskFileExists) {
        // if the task is complete but the file does not exist, we remove the task
        // and initiate a new one
        await FlutterDownloader.remove(taskId: task.taskId);
        // continue and initiate a new task
      } else {
        // task already exists and is enqueued / running / complete
        return task;
      }
    }

    // ensure bg-downloads directory exists
    final savedDir = await _saveDirectory();
    final fileName = "$prefix${url.split('/').last}";
    final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savedDir.absolute.path,
        showNotification:
            false, // show download progress in status bar (for Android)
        openFileFromNotification:
            true, // click on notification to open downloaded file (for Android)
        saveInPublicStorage: true,
        fileName: fileName);
    if (taskId == null) throw Exception("Failure while starting download");
    final tasks = (await FlutterDownloader.loadTasksWithRawQuery(
            query: "SELECT * from task WHERE task_id = '$taskId'")) ??
        [];
    return BgDownloadTask._fromDownloadTask(tasks.first);
  }

  Future<BgDownloadTask> startDownload(String url,
      {bool deleteOldDownload = false, int retries = 3}) async {
    if (retries <= 0) {
      throw Exception("Exceeded maximum retries for $url");
    }

    if (!downloadedTasks.containsKey(url)) {
      downloadedTasks[url] = _startDownload(url);
    }
    final task = await downloadedTasks[url]!;

    // Check if the cached task is complete but file doesn't exist
    // here downloadedTasks is a cache of ongoing downloads in the current session
    if (task.status == DownloadTaskStatus.complete) {
      final bool fileExists = await _checkIfFileExists(task.downloadPath);
      if (!fileExists) {
        printDebug(
            "File doesn't exist for completed task. Retrying... (${3 - retries + 1}/3) for URL: $url");
        // Remove from cache and restart download
        downloadedTasks.remove(url);
        return await startDownload(url,
            deleteOldDownload: deleteOldDownload, retries: retries - 1);
      }
      return task;
    } else if (task.status == DownloadTaskStatus.failed) {
      // As the task has failed, we remove it from the cache
      downloadedTasks.remove(url);
      return task;
    } else {
      return (await getDownloadTaskForUrl(url)) ?? task;
    }
  }

  void addListener(String taskId, DownloadTaskListener listener) {
    final taskListeners = listeners[taskId] ?? <DownloadTaskListener>[];
    taskListeners.add(listener);
    listeners[taskId] = taskListeners;
  }

  void removeListener(String taskId, DownloadTaskListener listener) {
    final taskListeners = listeners[taskId] ?? <DownloadTaskListener>[];
    taskListeners.remove(listener);
    listeners[taskId] = taskListeners;
  }

  void _startListening() {
    // incases like hot restart or process death, cleanup might not have happened
    // so it is safer to invoke remove port mapping before registering port with name
    IsolateNameServer.removePortNameMapping('downloader_send_port');
    IsolateNameServer.registerPortWithName(
        port.sendPort, 'downloader_send_port');
    port.listen((dynamic data) {
      String id = data[0];
      DownloadTaskStatus status = DownloadTaskStatus.fromInt(data[1]);
      int progress = data[2];
      _invokeListeners(id, status, progress);
    });
  }

  void _stopListening() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
  }

  void _invokeListeners(
      String taskId, DownloadTaskStatus status, int progress) {
    logInfo("taskId: $taskId, status: $status, progress: $progress");
    final taskListeners = listeners[taskId] ?? <DownloadTaskListener>[];
    if (taskListeners.isNotEmpty) {
      for (DownloadTaskListener listener in taskListeners) {
        listener.call(taskId, status, progress);
      }
    }
  }

  Future<Directory> _saveDirectory() async {
    Directory directory;
    if (Platform.isAndroid) {
      final path = await AndroidDownloadPath.downloadPath();
      directory = Directory(path);
    } else {
      directory = await getApplicationDocumentsDirectory();
    }
    return directory;
  }
}

class BgDownloadTask {
  final String taskId;
  final String url;
  final DownloadTaskStatus status;
  final String downloadPath;
  final int progress;

  const BgDownloadTask({
    required this.taskId,
    required this.url,
    required this.status,
    required this.downloadPath,
    required this.progress,
  });

  @override
  String toString() {
    return 'BgDownloadTask{taskId: $taskId, url: $url, status: $status, downloadPath: $downloadPath, progress: $progress}';
  }

  factory BgDownloadTask._fromDownloadTask(DownloadTask downloadTask) {
    return BgDownloadTask(
      taskId: downloadTask.taskId,
      url: downloadTask.url,
      status: downloadTask.status,
      progress: downloadTask.progress,
      downloadPath: p.join(downloadTask.savedDir, downloadTask.filename),
    );
  }
}

/// Put this widget at the root of your app, so that we can start listening
/// to download status, progress updates that are happening in the background
/// And also cleanup properly, when the UI is going away
class BgDownloaderHost extends StatefulWidget {
  final Widget child;

  const BgDownloaderHost({super.key, required this.child});

  @override
  State<StatefulWidget> createState() {
    return _BgDownloaderHostState();
  }
}

class _BgDownloaderHostState extends State<BgDownloaderHost> {
  late BgDownloader bgDownloader;

  @override
  void initState() {
    super.initState();
    bgDownloader = GetIt.I.get<BgDownloader>();
    bgDownloader._startListening();
  }

  @override
  void dispose() {
    bgDownloader._stopListening();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

@pragma('vm:entry-point')
Future<void> downloadCallback(String id, int status, int progress) async {
  try {
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send?.send([id, status, progress]);
  } catch (e, st) {
    printDebug("Error in downloadCallback");
    developer.log(
      "$e",
      name: "DownloadCallback",
      zone: Zone.current.errorZone,
      level: 1200,
      stackTrace: st,
      error: e,
    );
  }
}
