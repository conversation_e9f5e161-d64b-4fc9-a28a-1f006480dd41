import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/widgets/animated_progress_bar.dart';
import 'package:praja/enums/poster_share_destination.dart';
import 'package:praja/enums/poster_type.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/poster_photo_update/poster_photo_selection_page.dart';
import 'package:praja/features/posters/widgets/poster_share_actions_single_line_widget.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_helper.dart';
import 'package:praja/features/video_posters/video_poster_carousel_view_model.dart';
import 'package:praja/features/video_posters/widgets/video_poster_element_widget.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/common/deeplink_params.dart';

class VideoPosterCarouselWidget extends StatefulWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;
  final VoidCallback? onOperationStarted;
  final VoidCallback? onOperationEnded;

  const VideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
    this.onOperationStarted,
    this.onOperationEnded,
  });

  @override
  State<VideoPosterCarouselWidget> createState() =>
      _VideoPosterCarouselWidgetState();
}

class _VideoPosterCarouselWidgetState extends State<VideoPosterCarouselWidget> {
  late VideoPosterCarouselViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = context.videoPosterCarouselViewModel(
      key: '${widget.carousel.feedItemId}_${widget.carousel.videoFrameId}',
      carousel: widget.carousel,
      onOperationStarted: widget.onOperationStarted,
      onOperationEnded: widget.onOperationEnded,
    );
  }

  Widget _generationAndDownloadUI(VideoPosterCarouselState state) {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (state is VideoPosterCarouselGenerating)
              const SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  color: Colors.green,
                  strokeWidth: 2,
                ),
              )
            else if (state is VideoPosterCarouselDownloading)
              LiveDataBuilder(
                liveData: _viewModel.progress,
                builder: (context, progress) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Column(
                      children: [
                        Text(
                          // ignore: avoid_hardcoded_strings_in_ui
                          '${progress.toInt()}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        // Animated progress bar
                        AnimatedProgressBar(
                          value: progress / 100,
                          borderRadius: BorderRadius.circular(10),
                          minHeight: 8,
                          color: Colors.green,
                          backgroundColor: Colors.white.withOpacity(0.3),
                        ),
                      ],
                    ),
                  );
                },
              ),
            const SizedBox(height: 16),
            const Text(
              'వీడియో పోస్టర్ సిద్ధమవుతోంది...',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              VideoPosterCarouselHelper.getActionText(
                context,
                _viewModel.pendingActionMethod,
              ),
              textAlign: TextAlign.center,
              style: const TextStyle(color: Color(0xFFB2B2B2), fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _successUI() {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Success icon with circular background
              Container(
                width: 30,
                height: 30,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(height: 16),
              // Success text
              Text(
                VideoPosterCarouselHelper.getSuccessText(
                  context,
                  _viewModel.pendingActionMethod,
                ),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _errorUI(String message) {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 30,
                height: 30,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                context.getString(StringKey.videoPosterFailedText),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // Error description text
              Text(
                context.getString(StringKey.videoPosterFailedDescription),
                style: const TextStyle(
                  color: Color(0xFFB2B2B2),
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // Error message from state
              Text(
                message,
                style: const TextStyle(
                  color: Color(0xFFB2B2B2),
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _viewModel.onRetryClicked,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.all(8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        context.getString(StringKey.retryLabel),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _viewModel.onCloseErrorClicked,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white, width: 1),
                        padding: const EdgeInsets.all(8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        context.getString(StringKey.closeLabel),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final aspectRatio =
        widget.carousel.frameWidth / widget.carousel.frameHeight;

    return EventListener<VideoPosterCarouselEvent>(
      eventQueue: _viewModel.eventQueue,
      onEvent: (ctx, event) async {
        if (event is VideoPosterShareEvent) {
          final deeplinkUrl = event.deeplinkUrl;
          try {
            final isWhatsappOrExternalShare =
                event.destination == PosterShareDestination.whatsapp ||
                    event.destination == PosterShareDestination.externalShare;

            if (isWhatsappOrExternalShare) {
              await VideoPosterCarouselHelper.handleShare(
                event.filePath,
                event.shareText,
                event.destination,
              );
            }

            if (deeplinkUrl != null && deeplinkUrl.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (!mounted) return;
                DeeplinkDestination.fromRoute(deeplinkUrl)
                    ?.go(context, DeeplinkSource.internalDeeplink);
              });
            }
          } catch (e) {
            printDebug('Error handling video poster share: $e');
          }
        } else if (event is VideoPosterLockedEvent) {
          if (event.deeplink.isNotEmpty) {
            DeeplinkDestination.fromRoute(event.deeplink)
                ?.go(context, DeeplinkSource.internalDeeplink);
          }
        } else if (event is VideoPosterPhotoUpdatedEvent) {
          if (event.deeplink.isNotEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!mounted) return;
              DeeplinkDestination.fromRoute(event.deeplink)
                  ?.go(context, DeeplinkSource.internalDeeplink);
            });
          }
        }
      },
      child: LiveDataBuilder(
        liveData: _viewModel.state,
        builder: (context, state) {
          return ViewDetector(
            uniqueId:
                "video_poster_carousel_${widget.carousel.feedItemId}_${widget.source}",
            threshold: 1.0,
            onView: (_) {
              AppAnalytics.onPosterViewed(
                posterType: PosterType.video,
                parameters: {
                  'page_index': widget.pageIndex,
                  ...(widget.carousel.analyticsParams ?? {}),
                },
                source: widget.source,
              );
              // Track video poster impression when viewed
              context.impressionTracker()
                ..trackVideoCreative(widget.carousel.videoCreativeId.toString())
                ..trackVideoFrame(widget.carousel.videoFrameId.toString());
            },
            builder: (_, __) => Stack(
              children: [
                // Video poster carousel and Share actions
                Column(
                  children: [
                    // Top spacer
                    const Spacer(),

                    // Video poster preview - takes available height and full width
                    AspectRatio(
                      aspectRatio: aspectRatio,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: SizedBox(
                          width: widget.carousel.frameWidth,
                          height: widget.carousel.frameHeight,
                          child: Stack(
                            children: [
                              // Video poster elements - Frame, Protocol, Video, Plate & User photo
                              for (final element in widget.carousel.elements)
                                LiveDataBuilder(
                                  liveData: _viewModel.disappearCameraIcon,
                                  builder: (context, disappearCameraIcon) {
                                    return VideoPosterElementWidget(
                                      element: element,
                                      onUserWatchedVideoTillTriggerDuration:
                                          _viewModel
                                              .onUserWatchedVideoTillTriggerDuration,
                                      uniqueId:
                                          '${widget.carousel.feedItemId}_${element.x}_${element.y}',
                                      triggerDurationSeconds: widget
                                          .carousel.triggerAutoGenerationAfter,
                                      showCameraIcon: element.isUserPhoto &&
                                          !disappearCameraIcon,
                                      onCameraIconClicked: !element.isUserPhoto
                                          ? null
                                          : () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      PosterPhotoSelectionPage(
                                                    source:
                                                        "video_poster_carousel_${widget.carousel.feedItemId}_${widget.source}",
                                                    userPhotoType: widget
                                                        .carousel.userPhotoType,
                                                    onPhotoUpdated:
                                                        (updatedPhoto) {
                                                      _viewModel
                                                          .onPosterPhotoUpdated(
                                                              updatedPhoto);
                                                    },
                                                  ),
                                                ),
                                              );
                                            },
                                      onUserPhotoTapped: element.isUserPhoto
                                          ? _viewModel.onUserPhotoTapped
                                          : null,
                                    );
                                  },
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Bottom spacer
                    const Spacer(),

                    // Share actions
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: PosterShareActionsSingleLineWidget(
                        onDownloadClicked: _viewModel.onDownloadClicked,
                        onShareClicked: _viewModel.onShareClicked,
                        onWhatsappClicked: _viewModel.onWhatsappClicked,
                        minimumActionSize: const Size.fromHeight(40),
                      ),
                    ),

                    // Bottom spacer to prevent buttons from going to the very bottom
                    const Spacer(),
                  ],
                ),

                // Full-page overlays for generating, downloading, success, and error states
                if (state is VideoPosterCarouselGenerating ||
                    state is VideoPosterCarouselDownloading)
                  Positioned.fill(child: _generationAndDownloadUI(state)),

                // Success overlay (brief success indication)
                if (state is VideoPosterCarouselSuccess)
                  Positioned.fill(child: _successUI()),

                // Error overlay with close button
                if (state is VideoPosterCarouselError)
                  Positioned.fill(child: _errorUI(state.message)),
              ],
            ),
          );
        },
      ),
    );
  }
}
