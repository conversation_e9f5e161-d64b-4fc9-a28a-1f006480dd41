import 'package:praja/features/video_posters/models/video_poster_carousel.dart';

// Frame dimensions constants
const int portraitFrameWidth = 360;
const int portraitFrameHeight = 550;
const int squareFrameWidth = 360;
const int squareFrameHeight = 420;
const int landscapeFrameWidth = 360;
const int landscapeFrameHeight = 358;

// Common element dimensions
const int protocolImageWidth = 360;
const int protocolImageHeight = 104;
const int identityPlateWidth = 360;
const int identityPlateHeight = 50;

// Portrait video dimensions
const int portraitVideoWidth = 198;
const int portraitVideoHeight = 352;
const int portraitUserPhotoWidth =
    180; // Reduced by 20% while maintaining 250:280 aspect ratio (180x202)
const int portraitUserPhotoHeight = 202;

// Square video dimensions
const int squareVideoWidth = 330;
const int squareVideoHeight = 248;
const int squareUserPhotoWidth =
    116; // Reduced by 20% while maintaining 250:280 aspect ratio (116x130)
const int squareUserPhotoHeight = 130;

// Landscape video dimensions
const int landscapeVideoWidth = 330;
const int landscapeVideoHeight = 188;
const int landscapeUserPhotoWidth =
    116; // Reduced by 20% while maintaining 250:280 aspect ratio (116x130)
const int landscapeUserPhotoHeight = 130;

// Padding and spacing constants
const int framePadding = 16;
const int videoPadding = 15;
const int identitySpacing = 8;

const Map<String, dynamic> mockVideoPosterCarouselJson = {
  'feed_type': 'video_posters_carousel',
  'feed_item_id': "mock_video_feed_item_id_123",
  'video_creative_id': 19,
  'frame_height': portraitFrameHeight,
  'frame_width': portraitFrameWidth,
  'video_mode': "PORTRAIT",
  'video_frame_id': 2,
  'elements': [
    // First element - Background gradient frame
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/JSP.png",
      "width": portraitFrameWidth,
      "height": portraitFrameHeight,
      "x": 0,
      "y": 0,
      "type": "photo"
    },
    // Second element - Protocol image
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-1177181-1749724292188.png",
      "width": protocolImageWidth,
      "height": protocolImageHeight,
      "x": 0,
      "y": 0,
      "type": "photo"
    },
    // Third element - Video
    // x = frame_width - framePadding - video_width = 360 - 16 - 198 = 146
    // y = frame_height - identity_height - identitySpacing - video_height = 550 - 50 - 8 - 352 = 140
    {
      "url":
          "https://ruv-cdn.thecircleapp.in/assets01/e64bd775b7a7cdee6e2b6502bfa1b85f.mp4",
      "width": portraitVideoWidth,
      "height": portraitVideoHeight,
      "x": portraitFrameWidth - framePadding - portraitVideoWidth,
      "y": portraitFrameHeight -
          identityPlateHeight -
          identitySpacing -
          portraitVideoHeight,
      "type": "video",
      "border": {"radius": 20, "width": 1, "color": 0xff000000}
    },
    // Fourth element - User profile photo
    // y = frame_height - identity_height - user_photo_height
    {
      "url":
          "https://a-cdn.thecircleapp.in/cutouts/01JXC97H883PF2556Z1MHYR83N.png",
      "width": portraitUserPhotoWidth,
      "height": portraitUserPhotoHeight,
      "x": 0,
      "y": portraitFrameHeight - identityPlateHeight - portraitUserPhotoHeight,
      "type": "photo",
      "is_user_photo": true
    },
    // Fifth element - Identity plate
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/user-protocol-photos/identity.png",
      "width": identityPlateWidth,
      "height": identityPlateHeight,
      "x": 0,
      "y": portraitFrameHeight - identityPlateHeight,
      "type": "photo"
    }
  ],
  'share_text': 'Check out this video poster!',
  'locked_deeplink': '',
  'analytics_params': null,
  'enable_auto_generation': true,
  'trigger_auto_generation_after': 5,
  'video_poster_deeplink': '/video-posters?source=photo_update'
};

const Map<String, dynamic> mockVideoPosterCarouselSquareJson = {
  'feed_type': 'video_posters_carousel',
  'feed_item_id': "mock_video_feed_item_id_square_456",
  'video_creative_id': 19,
  'frame_height': squareFrameHeight,
  'frame_width': squareFrameWidth,
  'video_mode': "SQUARE",
  'video_frame_id': 3,
  'elements': [
    // First element - Background gradient frame
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/user-protocol-photos/tysrcp.png",
      "width": squareFrameWidth,
      "height": squareFrameHeight,
      "x": 0,
      "y": 0,
      "type": "photo"
    },
    // Second element - Protocol image
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-1653960-1749834980392.png",
      "width": protocolImageWidth,
      "height": protocolImageHeight,
      "x": framePadding,
      "y": framePadding,
      "type": "photo"
    },
    // Third element - Video
    // x = frame_width - video_width - videoPadding
    // y = frame_height - video_height - identity_height - videoPadding
    {
      "url":
          "https://ruv-cdn.thecircleapp.in/assets01/065b31700d0909ec9a4622fb301eab04.mp4",
      "width": squareVideoWidth,
      "height": squareVideoHeight,
      "x": squareFrameWidth - squareVideoWidth - videoPadding,
      "y": squareFrameHeight -
          squareVideoHeight -
          identityPlateHeight -
          videoPadding,
      "type": "video",
      "border": {"radius": 20, "width": 1, "color": 0xff000000}
    },
    // Fourth element - Identity plate
    // y = frame_height - identity_height
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/admin-media/40/b20cc1e9-7033-4031-968b-664c59677bb9.png",
      "width": identityPlateWidth,
      "height": identityPlateHeight,
      "x": 0,
      "y": squareFrameHeight - identityPlateHeight,
      "type": "photo"
    },
    // Fifth element - User profile photo
    // y = frame_height - user_photo_height
    {
      "url":
          "https://a-cdn.thecircleapp.in/cutouts/01JXC97H883PF2556Z1MHYR83N.png",
      "width": squareUserPhotoWidth,
      "height": squareUserPhotoHeight,
      "x": 0,
      "y": squareFrameHeight - squareUserPhotoHeight,
      "type": "photo",
      "is_user_photo": true
    }
  ],
  'share_text': 'Check out this square video poster!',
  'locked_deeplink': '',
  'analytics_params': null,
  'enable_auto_generation': true,
  'trigger_auto_generation_after': 5,
  'video_poster_deeplink': '/video-posters?source=photo_update'
};

const Map<String, dynamic> mockVideoPosterCarouselLandscapeJson = {
  'feed_type': 'video_posters_carousel',
  'feed_item_id': "mock_video_feed_item_id_landscape_789",
  'video_creative_id': 19,
  'frame_height': landscapeFrameHeight,
  'frame_width': landscapeFrameWidth,
  'video_mode': "LANDSCAPE",
  'video_frame_id': 1,
  'elements': [
    // First element - Background gradient frame
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/JSP.png",
      "width": landscapeFrameWidth,
      "height": landscapeFrameHeight,
      "x": 0,
      "y": 0,
      "type": "photo"
    },
    // Second element - Protocol image
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-1177181-1749724292188.png",
      "width": protocolImageWidth,
      "height": protocolImageHeight,
      "x": 0,
      "y": 0,
      "type": "photo"
    },
    // Third element - Video
    // x = frame_width - video_width - videoPadding
    // y = frame_height - video_height - identity_height - videoPadding (adjusted for landscape)
    {
      "url":
          "https://ruv-cdn.thecircleapp.in/assets01/67d5a4dcf00239971dbb9d8132af7152.mp4",
      "width": landscapeVideoWidth,
      "height": landscapeVideoHeight,
      "x": landscapeFrameWidth - landscapeVideoWidth - videoPadding,
      "y":
          landscapeFrameHeight - landscapeVideoHeight - identityPlateHeight - 9,
      "type": "video",
      "border": {"radius": 20, "width": 1, "color": 0xff000000}
    },
    // Fourth element - Identity plate
    // y = frame_height - identity_height
    {
      "url":
          "https://a-cdn.thecircleapp.in/production/admin-media/40/b20cc1e9-7033-4031-968b-664c59677bb9.png",
      "width": identityPlateWidth,
      "height": identityPlateHeight,
      "x": 0,
      "y": landscapeFrameHeight - identityPlateHeight,
      "type": "photo"
    },
    // Fifth element - User profile photo
    // y = frame_height - user_photo_height
    {
      "url":
          "https://a-cdn.thecircleapp.in/cutouts/01JXC97H883PF2556Z1MHYR83N.png",
      "width": landscapeUserPhotoWidth,
      "height": landscapeUserPhotoHeight,
      "x": 0,
      "y": landscapeFrameHeight - landscapeUserPhotoHeight,
      "type": "photo",
      "is_user_photo": true
    }
  ],
  'share_text': 'Check out this landscape video poster!',
  'locked_deeplink': '',
  'analytics_params': null,
  'enable_auto_generation': true,
  'trigger_auto_generation_after': 5,
  'video_poster_deeplink': '/video-posters?source=photo_update'
};

VideoPosterCarousel getMockVideoPosterCarousel() {
  return VideoPosterCarousel.fromJson(mockVideoPosterCarouselJson);
}

VideoPosterCarousel getMockVideoPosterCarouselSquare() {
  return VideoPosterCarousel.fromJson(mockVideoPosterCarouselSquareJson);
}

VideoPosterCarousel getMockVideoPosterCarouselLandscape() {
  return VideoPosterCarousel.fromJson(mockVideoPosterCarouselLandscapeJson);
}
