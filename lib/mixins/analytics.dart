import 'dart:io';

import 'package:async/async.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:get_it/get_it.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:praja/enums/app_environment.dart';
import 'package:praja/enums/media_upload_status.dart';
import 'package:praja/enums/poster_type.dart';
import 'package:praja/extensions/map_extensions.dart';
import 'package:praja/features/payments/models/upi_app.dart';
import 'package:praja/features/premium_experience/models/upgrade_feed_item.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics_config.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/cloud_media_file.dart';
import 'package:praja/screens/create/createPage/bloc/create_bloc_states.dart';
import 'package:praja/services/event_flag.dart';
import 'package:praja/services/permissions.dart';
import 'package:praja/utils/logger.dart';
import 'package:singular_flutter_sdk/singular.dart';

class AppAnalytics {
  // static String _distinctId;
  // static MixpanelAPI _mixpanelInstance;
  static late Mixpanel _mixpanel;

  static bool _isInitialized = false;

  static Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    _isInitialized = true;
    FutureGroup futureGroup = FutureGroup();

    futureGroup.add(Firebase.initializeApp());
    futureGroup.add(initializeMixpanel());

    futureGroup.close();
    await futureGroup.future;
  }

  static Future<void> initializeMixpanel() async {
    _mixpanel = await Mixpanel.init(
        AppEnvironment.current() == AppEnvironment.production
            ? "e7296da579ccb6e3af1da931836a7bf5"
            : "fc09ac79a76a9939f914ddf8f48c7725",
        trackAutomaticEvents: true);

    await _setSuperProperties();
    // _mixpanel.setLoggingEnabled(true);
  }

  static Future<void> _setSuperProperties() async {
    _mixpanel.registerSuperProperties({
      "device_locale": Platform.localeName,
      "contacts_permission_status": await PermissionsService()
          .getContactsPermissionStatusForSuperProperties(),
      "notification_permission_status":
          await PermissionsService().getNotificationPermissionStatus(),
    });
  }

  // Helper Methods
  static FirebaseAnalytics getFirebaseInstance() {
    return FirebaseAnalytics.instance;
  }

  static Future<String?> getFirebaseAppInstanceId() async {
    return await getFirebaseInstance().appInstanceId;
  }

  static void registerSessionSuperProperties(Map<String, dynamic> props) {
    _mixpanel.registerSuperProperties(props);
  }

  static void setAppUser(AppUser user, [bool postLogin = false]) {
    // _distinctId = user.id.toString();
    String userId = user.id.toString();
    getFirebaseInstance().setUserId(id: userId);

    FirebaseCrashlytics.instance.setUserIdentifier(userId);

    // Disable firebase analytics for internal accounts
    if (user.internal) {
      getFirebaseInstance().setAnalyticsCollectionEnabled(false);
    } else {
      getFirebaseInstance().setAnalyticsCollectionEnabled(true);
    }

    _mixpanel.identify(userId);
    _setMixpanelAppUserProperties(user);
  }

  static void logShareOrDownloadFail(String error, int elementId,
      {bool isWhatsapp = false,
      bool isGenericShare = false,
      bool isDownload = false,
      String contentType = "post"}) {
    final params = {
      "element_id": elementId.toString(),
      "is_whatsapp": isWhatsapp.toString(),
      "is_generic": isGenericShare.toString(),
      "is_download": isDownload.toString(),
      "error": error.toString(),
      "content_type": contentType.toString()
    };
    AppAnalytics.logEvent(name: "share_fail", parameters: params);
  }

  static void setAnalyticsCollectionEnabled(bool enabled) async {
    getFirebaseInstance().setAnalyticsCollectionEnabled(enabled);
    // todo: add mixpanel opt out functionality
    // https://github.com/amondnet/flutter_mixpanel/issues/3
  }

  static void logLogin({required AppUser user, required String loginMethod}) {
    getFirebaseInstance().logLogin(loginMethod: loginMethod);

    logEvent(
        name: "login",
        parameters: {"method": loginMethod},
        sendToFirebase: false);
    _mixpanel.identify(user.id.toString());
    _setMixpanelAppUserProperties(user);
    Singular.event("Login");
  }

  //Taking this out of setUser() because this is a "event" for singular rather than a config
  static void setSingularUserId(String userId) {
    Singular.setCustomUserId(userId);
    AppAnalytics.logMixPanelEvent(
        name: "D_Si Singular Set UserId", parameters: {"attr1": userId});
  }

  static void logSignUp({required AppUser user, required String signUpMethod}) {
    getFirebaseInstance().logSignUp(signUpMethod: signUpMethod);

    logEvent(
        name: "sign_up",
        parameters: {"method": signUpMethod},
        sendToFirebase: false);
    _mixpanel.identify(user.id.toString());
    _mixpanel.getPeople().setOnce("signup_method", signUpMethod);
    _setMixpanelAppUserProperties(user);

    Singular.event("Signup");
  }

  static void logLoginSignupBegin(
      {required String method,
      required String userType,
      required bool isInternal}) {
    AppAnalytics.logEvent(name: "login_signup_begin", parameters: {
      "method": method,
      "user_type": userType,
      "is_internal": isInternal
    });
  }

  static void _mixpanelTrack(String name, {Map<String, dynamic>? properties}) {
    if (properties != null) {
      properties.forEach((key, value) {
        if (key.length > 40) {
          throw (ArgumentError.value(key, "key",
              "mixpanel event parameter key length should be less than 40"));
        }
      });
    }

    _mixpanel.track(name, properties: properties);
  }

  static void _setMixpanelAppUserProperties(AppUser user) async {
    _mixpanel.getPeople().setOnce("\$phone", "+91${user.phone}");
    _mixpanel.getPeople().set("\$name", user.name);
    _mixpanel.getPeople().set("is_internal", user.internal);
    _mixpanel.getPeople().set("signed_up", true);
    _mixpanel.getPeople().set("village_id", user.village?.id);
    _mixpanel.getPeople().set("mandal_id", user.mandal?.id);
    _mixpanel.getPeople().set("district_id", user.district?.id);

    _mixpanel.getPeople().set("default_feed",
        user.isFeedExperimentUser ? "my_feed" : "trending_feed");

    final badge = user.badge;
    if (badge != null) {
      if (badge.active) {
        if (badge.description.isNotEmpty) {
          _mixpanel
              .getPeople()
              .set("badge_title", badge.description.toString());
        }
        _mixpanel.getPeople().set("badge_holder", "Yes");
      }
    }
  }

  static void logJoinCircle(Circle circle, String source,
      {Map<String, dynamic>? parameters}) {
    logJoinCircleEvent(
      id: circle.id,
      name: circle.name,
      source: source,
      parameters: parameters,
    );
  }

  static void logJoinCircleEvent({
    required int id,
    required String name,
    required String source,
    Map<String, dynamic>? parameters,
  }) {
    Map<String, dynamic> params = {
      "circle_id": id,
      "circle_name": name,
      "source": source
    };
    if (parameters != null) {
      params.addAll(parameters);
    }
    getFirebaseInstance().logJoinGroup(groupId: id.toString());
    logEvent(name: 'join_circle', parameters: params);
  }

  static void logLeaveCircle(Circle circle,
      {String? source, Map<String, dynamic>? parameters}) {
    getFirebaseInstance().logEvent(
      name: 'leave_group',
      parameters: {"group_id": circle.id.toString()},
    );
    Map<String, dynamic> params = {
      "circle_id": circle.id.toString(),
      "source": source ?? "unknown"
    };
    if (parameters != null) {
      params.addAll(parameters);
    }
    logEvent(name: 'leave_circle', parameters: params);
  }

  static void logAppOpenByReturnUsers(String source,
      {String notificationProvider = "NA"}) {
    if (!GetIt.I.get<EventFlags>().appOpenByReturnUsersTriggered) {
      AppAnalytics.logEvent(name: "app_open_by_return_users", parameters: {
        "source": source,
        "notification_provider": notificationProvider.toString()
      });
      GetIt.I.get<EventFlags>().appOpenByReturnUsersTriggered = true;
    }
  }

  static void logFeedError(String msg, String statusCode, String source,
      {String error = "-"}) {
    AppAnalytics.logEvent(name: 'feed_error', parameters: {
      "status_code": statusCode,
      "msg": msg,
      "source": source,
      "status_message": error
    });
  }

  static void logBadgeCelebration(String source) {
    AppAnalytics.logEvent(
        name: "show_badge_celebration", parameters: {"source": source});
  }

  static void logLogOut() {
    AppAnalytics.logEvent(name: "logout");
    _mixpanel.reset();

    _setSuperProperties();
  }

  static logProfileSetStarted(String source, [bool isImageExists = false]) {
    AppAnalytics.logEvent(name: "profile_pic_set_started", parameters: {
      "source": source,
      "pic_exists": isImageExists ? "Yes" : "No"
    });
  }

  static logProfilePictureUpdate(String source, String? mode,
      {bool state = false}) {
    AppAnalytics.logEvent(name: "profile_picture_updated", parameters: {
      "source": source,
      "mode": mode ?? "unknown",
      "state": state ? "successful" : "unsuccessful"
    });
  }

  static logProfilePictureDeletionStarted() {
    AppAnalytics.logEvent(name: "profile_picture_deletion started");
  }

  static logProfilePictureDeletion({bool isSuccessful = false}) {
    AppAnalytics.logEvent(name: "profile_picture_deletion", parameters: {
      "state": isSuccessful ? "Yes" : "No",
    });
  }

  static logSearchCleared({required String source, required String query}) {
    AppAnalytics.logEvent(name: "search_cleared", parameters: {
      "source": source,
      "query": query,
    });
  }

  static void logEvent(
      {required String name,
      Map<String, dynamic>? parameters,
      bool sendToFirebase = true}) {
    parameters ??= {};

    logInfo(
      "Log Event : [$name] , parameters : [${parameters.entries.map((entry) => "${entry.key}:${entry.value}").toList().join(",")}]",
    );

    final analyticsConfig = GetIt.I.get<AnalyticsConfig>();

    List<String> singularEvents = analyticsConfig.singularEvents
        .map((event) => event.toString())
        .toList();

    if (sendToFirebase) {
      Map<String, String> transformedParameters = {};

      parameters.forEach((key, value) {
        if (value is List && value.isNotEmpty) {
          transformedParameters[key] = value.join(", ");
        } else if (value is List && value.isEmpty) {
          transformedParameters[key] = "null";
        } else {
          transformedParameters[key] = value.toString();
        }
      });
      getFirebaseInstance()
          .logEvent(name: name, parameters: transformedParameters);
    }

    // Fire Singular Event if the Event Name is Coming from Get Initial Data Singular Events List
    if (singularEvents.contains(name)) {
      Singular.eventWithArgs(name, parameters);
      if (name == "share") {
        final contentType = parameters["content_type"];
        final eventName = "${contentType}_share";
        Singular.eventWithArgs(eventName, parameters);
      }
    }

    _mixpanelTrack(name, properties: parameters);
  }

  static timeEvent({required String name}) {
    _mixpanel.timeEvent(name);
  }

  static void logMixPanelEvent(
      {required String name, Map<String, dynamic>? parameters}) {
    Map<String, dynamic> transformedParameters = {};

    if (parameters == null) {
      parameters = {};
    } else {
      parameters.forEach((key, value) {
        transformedParameters[key] = value;
      });
    }

    logEvent(name: name, parameters: transformedParameters);
  }

  static void logAppOpen(String appDisplayMode, String appInstallationSource) {
    getFirebaseInstance().logAppOpen();
    logEvent(name: "App_open", parameters: {
      "system_display_mode": appDisplayMode,
      "install_source": appInstallationSource
    });
    logInfo(
      "Log Event : [App_open] , system_display_mode: $appDisplayMode, install_source: $appInstallationSource ]",
    );

    Singular.event("App Launched");
  }

  static void logAppClose() {
    getFirebaseInstance().logEvent(name: "app_close");
    logEvent(name: "App_closed");
    logInfo(
      "Log Event : [App_closed] ]",
    );
  }

  static void logProfileVisit(int visitorId, int visitedProfileId,
      {required String source}) {
    logEvent(name: "Profile_visit", parameters: {
      "visitor_id": visitorId,
      "visited_profile_id": visitedProfileId,
      "source": source
    });
    logInfo(
      "Log Event : [Profile_visit] ,visitor_id: $visitorId, visitedProfileId: $visitedProfileId, source: $source ]",
    );
  }

  static void logSelectContent(
      {required String contentType, required int itemId}) {
    getFirebaseInstance().logSelectContent(
      contentType: contentType,
      itemId: itemId.toString(),
    );

    logEvent(
      name: "view_$contentType",
      parameters: {"item_id": itemId},
    );
  }

  static void logShare({
    required String contentType,
    required String itemId,
    required String method,
    Map<String, dynamic>? otherParameters,
  }) {
    getFirebaseInstance().logShare(
      contentType: contentType,
      itemId: itemId,
      method: method,
    );
    final parameters = {
      "content_type": contentType,
      "item_id": itemId,
      "method": method,
      ...?otherParameters
    };

    logEvent(name: "share", parameters: parameters, sendToFirebase: false);
  }

  static Future<void> flush() async {
    await Future.delayed(const Duration(milliseconds: 1));
    _mixpanel.flush();
  }

  static onRefreshFCMToken(String token) {
    _mixpanel.getPeople().union("\$android_devices", [token]);
  }

  static void onDefaultFeedLoaded(String feedType) {
    logEvent(name: "default_feed_loaded", parameters: {"feed_type": feedType});
  }

  static logImageAnalytics(
    MediaUploadStatus stage,
    String source,
    String? error, {
    int? imageWidth,
    int? imageHeight,
    int? sourceSize,
    int? uploadSize,
    String? fileId,
    int? httpCode,
  }) {
    Map<String, dynamic> parameters = {
      "source": source,
      "stage": stage.toString()
    };
    if (imageWidth != null) {
      parameters["image_width"] = imageWidth;
    }
    if (imageHeight != null) {
      parameters["image_height"] = imageHeight;
    }
    if (sourceSize != null) {
      parameters["source_size"] = sourceSize;
    }
    if (uploadSize != null) {
      parameters["upload_size"] = uploadSize;
    }
    if (fileId != null) {
      parameters["file_id"] = fileId;
    }
    if (error != null && error.isNotEmpty) {
      parameters["error"] = error;
    }
    if (httpCode != null) {
      parameters["http_code"] = httpCode;
    }
    AppAnalytics.logEvent(
      name: "upload_image_to_cloud",
      parameters: parameters,
    );
  }

  static Future<void> logEachImageOnUpload(
      {required List<NewPhoto> compressedPhotos,
      required List<NewPhoto> sourcePhotos,
      List<CloudImageFile>? imageFiles,
      required String source,
      required MediaUploadStatus status}) async {
    for (int i = 0; i < compressedPhotos.length; i++) {
      final sourceImageFile = sourcePhotos[i];
      AppAnalytics.logImageAnalytics(
        status,
        source,
        null,
        imageHeight: compressedPhotos[i].height,
        imageWidth: compressedPhotos[i].width,
        sourceSize: await File(sourceImageFile.data.path).length(),
        uploadSize: await File(compressedPhotos[i].data.path).length(),
        fileId: imageFiles?[i].fileId,
      );
    }
  }

  static logVideoAnalytics(
    MediaUploadStatus stage,
    String source,
    String? error, {
    int? duration,
    double? sourceBitrate,
    int? sourceSize,
    int? sourceHeight,
    int? sourceWidth,
    double? uploadBitrate,
    int? uploadSize,
    int? uploadHeight,
    int? uploadWidth,
    String? videoMode,
    String? fileId,
    int? httpCode,
  }) {
    Map<String, dynamic> parameters = {
      "source": source,
      "stage": stage.toString(),
    };
    if (error != null && error.isNotEmpty) {
      parameters["error"] = error;
    }

    if (duration != null) {
      parameters["duration_secs"] = duration;
    }
    if (sourceBitrate != null) {
      parameters["source_bitrate_mbps"] = sourceBitrate;
    }
    if (sourceSize != null) {
      parameters["source_size_bits"] = sourceSize;
    }
    if (sourceHeight != null) {
      parameters["source_height"] = sourceHeight;
    }
    if (sourceWidth != null) {
      parameters["source_width"] = sourceWidth;
    }
    if (videoMode != null) {
      parameters["video_mode"] = videoMode;
    }
    if (uploadBitrate != null) {
      parameters["upload_bitrate_mbps"] = uploadBitrate;
    }
    if (uploadSize != null) {
      parameters["upload_size_bits"] = uploadSize;
    }
    if (uploadHeight != null) {
      parameters["upload_height"] = uploadHeight;
    }
    if (uploadWidth != null) {
      parameters["upload_width"] = uploadWidth;
    }
    if (fileId != null) {
      parameters["file_id"] = fileId;
    }
    if (httpCode != null) {
      parameters["http_code"] = httpCode;
    }
    AppAnalytics.logEvent(
      name: "upload_video_to_cloud",
      parameters: parameters,
    );
  }

  static void logPageView(String pageName, Map<String, dynamic> properties) {
    logEvent(name: "viewed_${pageName}_page", parameters: properties);
  }

  static void logTabViewed(String tabName, Map<String, dynamic> properties) {
    logEvent(name: "viewed_${tabName}_tab", parameters: properties);
  }

  static void onChatClicked(
      {required Map<String, dynamic> conversationParams,
      required int unreadCount,
      required String inboxType,
      required int index}) {
    logEvent(
        name: "clicked_chat",
        parameters: {
          "unread_count": unreadCount,
          "inbox_type": inboxType,
          "index": index,
        }..addAll(conversationParams));
  }

  static void onNewChatClicked({
    required String source,
  }) {
    logEvent(name: "clicked_new_chat", parameters: {
      "source": source,
    });
  }

  static void onChatDetailsClicked({
    required Map<String, dynamic> params,
    required String inboxType,
    required String source,
  }) {
    logEvent(
        name: "clicked_chat_details",
        parameters: {
          "inbox_type": inboxType,
          "source": source,
        }..addAll(params));
  }

  static void onOtherChatsClicked({required int unreadCount}) {
    logEvent(name: "clicked_other_chats", parameters: {
      "unread_count": unreadCount,
    });
  }

  static void onMyChatsClicked({required int unreadCount}) {
    logEvent(
        name: "clicked_my_chats", parameters: {"unread_count": unreadCount});
  }

  static void onChatListLoaded(
      {required int primaryCount,
      required int otherCount,
      required int primaryUnreadCount,
      required int otherUnreadCount}) {
    logEvent(name: "loaded_chat_list", parameters: {
      "primary_count": primaryCount,
      "other_count": otherCount,
      "primary_unread_count": primaryUnreadCount,
      "other_unread_count": otherUnreadCount,
    });
  }

  static void onChatMuted({required Map<String, dynamic> params}) {
    logEvent(name: "muted_chat", parameters: params);
  }

  static void onChatUnmuted({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "unmuted_chat", parameters: params);
  }

  static void onClickedUserNameInChat({required Map<String, dynamic> params}) {
    logEvent(name: "clicked_user_name_chat", parameters: params);
  }

  static void onClickedCircleNameInChat(
      {required Map<String, dynamic> params}) {
    logEvent(name: "clicked_circle_name_chat", parameters: params);
  }

  static void onClickedUserPhotoInChat({required Map<String, dynamic> params}) {
    logEvent(name: "clicked_user_photo_chat", parameters: params);
  }

  static void onClickedCirclePhotoInChat(
      {required Map<String, dynamic> params}) {
    logEvent(name: "clicked_cicle_photo_chat", parameters: params);
  }

  static void onClickedViewProfileInChat(
      {required Map<String, dynamic> params}) {
    logEvent(name: "clicked_view_profile_chat", parameters: params);
  }

  static void onClickedViewCircleInChat(
      {required Map<String, dynamic> params}) {
    logEvent(name: "clicked_view_circle_chat", parameters: params);
  }

  static void onClickedBlockInChat({
    required int userId,
    required Map<String, dynamic> params,
    required String source,
  }) {
    logEvent(name: "clicked_block_user_chat", parameters: {
      "source": source,
    });
    // for backward compatibility
    logEvent(name: "user_block_begin", parameters: {
      "source": source,
      "content_type": "user",
      "item_id": userId,
    });
  }

  static void onClickedUnblockInChat({
    required Map<String, dynamic> params,
    required String source,
  }) {
    logEvent(name: "clicked_unblock_user_chat", parameters: {
      "source": source,
    });
  }

  static void onAcceptedChat({required Map<String, dynamic> params}) {
    logEvent(name: "accepted_chat", parameters: params);
  }

  static void onIgnoredChat(
      {required Map<String, dynamic> conversationParams,
      required Map<String, dynamic> otherUserParams}) {
    logEvent(
        name: "ignored_chat",
        parameters: {}
          ..addAll(conversationParams)
          ..addAll(otherUserParams.prefixed("other_")));
  }

  static void onImagesAttachedInChat({
    required int count,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "attached_images_chat",
        parameters: {
          "count": count,
        }..addAll(params));
  }

  static void onVideoAttachedInChat({required Map<String, dynamic> params}) {
    logEvent(name: "attached_video_chat", parameters: params);
  }

  static void onReplyToMessageChat({
    required String interaction,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "initiated_reply_message_chat",
        parameters: {
          "interaction": interaction,
        }..addAll(params));
  }

  static void onForwardClickedInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_forward_message_chat", parameters: params);
  }

  static void onMessageForwarded({
    required String sourceMessageId,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "forwarded_message_chat",
        parameters: {
          "source_message_id": sourceMessageId,
        }..addAll(params));
  }

  static void onPostSharedInChat(
      {required int postId, required Map<String, dynamic> params}) {
    logEvent(
        name: "shared_post_chat",
        parameters: {
          "post_id": postId,
        }..addAll(params));
  }

  static void onPostShareClick({
    required int postId,
    String? source = "unknown",
  }) {
    logEvent(name: "clicked_share_post", parameters: {
      "post_id": postId,
      "source": source,
    });
  }

  static void onTargetSelectedForForwarding({
    required String sourceMessageId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "selected_user_forward_message_chat",
        parameters: {
          "source_message_id": sourceMessageId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "target_source": targetSource,
        }..addAll(params));
  }

  static void onTargetUnselectedForForwarding({
    required String sourceMessageId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "unselected_user_forward_message_chat",
        parameters: {
          "source_message_id": sourceMessageId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "target_source": targetSource,
        }..addAll(params));
  }

  static void onUserSelectedForPostShareInChat({
    required int postId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "selected_user_share_post_chat",
        parameters: {
          "post_id": postId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "target_source": targetSource,
        }..addAll(params));
  }

  static void onTargetUnselectedForPostShareInChat({
    required int postId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "unselected_user_share_post_chat",
        parameters: {
          "post_id": postId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "target_source": targetSource,
        }..addAll(params));
  }

  static void onMaxTargetChatsSelectedForForwarding({
    required String sourceMessageId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "selected_max_users_forward_message_chat",
        parameters: {
          "source_message_id": sourceMessageId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "target_source": targetSource,
        }..addAll(params));
  }

  static void onMaxTargetChatsSelectedForPostShareInChat({
    required int postId,
    required String targetSource,
    required int selectedTargetChatsCount,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "selected_max_users_share_post_chat",
        parameters: {
          "post_id": postId,
          "selected_target_chats_count": selectedTargetChatsCount,
          "user_source": targetSource,
        }..addAll(params));
  }

  static void onMessageRetriedInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_retry_message_chat", parameters: params);
  }

  static void onMessageSentErrorInChat(
      {required String errorMessage,
      required int statusCode,
      required Map<String, dynamic> params}) {
    logEvent(
        name: "message_sent_error_chat",
        parameters: {
          'errorMessage': errorMessage,
          'statusCode': statusCode.toString()
        }..addAll(params));
  }

  static void onMessageDeletedInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_delete_message_chat", parameters: params);
  }

  static void onMessageCopiedInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_copy_message_chat", parameters: params);
  }

  static void onClickedGoToWebsiteInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_go_to_website_message_chat", parameters: params);
  }

  static void onPostPreviewClickedInChat({
    required int postId,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "clicked_post_preview_chat",
        parameters: {
          "post_id": postId,
        }..addAll(params));
  }

  static void onPostPreviewImageClickedInChat({
    required int postId,
    required int index,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "clicked_image_post_preview_chat",
        parameters: {
          "post_id": postId,
          "index": index,
        }..addAll(params));
  }

  static void onPostPreviewVideoClickedInChat(
      {required int postId, required Map<String, dynamic> params}) {
    logEvent(
        name: "clicked_video_post_preview_chat",
        parameters: {
          "post_id": postId,
        }..addAll(params));
  }

  static void onPostPreviewChildClickedInChat({
    required int childPostId,
    required int parentPostId,
    required Map<String, dynamic> params,
  }) {
    logEvent(
        name: "clicked_video_post_preview_chat",
        parameters: {
          "child_post_id": childPostId,
          "parent_post_id": parentPostId,
        }..addAll(params));
  }

  static void onAttachmentRemovedInChat(
      {required String type, required Map<String, dynamic> params}) {
    logEvent(
        name: "removed_attachment_chat",
        parameters: {
          "type": type,
        }..addAll(params));
  }

  static void onImageAttachmentClicked(
      {required int index, required Map<String, dynamic> params}) {
    logEvent(
        name: "clicked_image_attachment_chat",
        parameters: {
          "index": index,
        }..addAll(params));
  }

  static void onLinkPreviewClickedInChat(
      {required String link, required Map<String, dynamic> params}) {
    logEvent(
        name: "clicked_link_preview_chat",
        parameters: {
          "link": link,
        }..addAll(params));
  }

  static void onChannelPreviewClickedInChat(
      {required int circleId, required Map<String, dynamic> params}) {
    logEvent(
        name: "clicked_channel_preview_chat",
        parameters: {
          "channel_circle_id": circleId,
        }..addAll(params));
  }

  static void onVideoAttachmentClicked({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "clicked_video_attachment_chat", parameters: params);
  }

  static void onMessageSentInChat({
    required Map<String, dynamic> params,
  }) {
    logEvent(name: "sent_message_chat", parameters: params);
  }

  static void onMessageClickedInProfile({
    required int userId,
  }) {
    logEvent(name: "clicked_message_profile", parameters: {
      "user_id": userId,
    });
  }

  static void onSubscribeClicked(
      {required String orderId,
      required String source,
      Map<String, dynamic>? otherParameters}) {
    Map<String, dynamic> parameters = {
      "order_id": orderId,
      "source": source,
    };
    if (otherParameters != null) {
      parameters.addAll(otherParameters);
    }
    logEvent(name: "subscribe_clicked", parameters: parameters);
  }

  static void onPosterCategoryClicked({
    required int index,
    required String source,
    Map<String, dynamic>? parameters,
  }) {
    Map<String, dynamic> allParameters = {
      "category_index": index,
      "source": source,
    };
    if (parameters != null) {
      allParameters.addAll(parameters);
    }
    logEvent(name: "poster_category_clicked", parameters: allParameters);
  }

  static void onPosterCreativeSelected(
      {bool isPosterTapUX = false, Map<String, dynamic>? parameters}) {
    Map<String, dynamic> allParameters = {
      "is_poster_tap_ux": isPosterTapUX,
    };
    if (parameters != null) {
      allParameters.addAll(parameters);
    }
    logEvent(name: "poster_creative_selected", parameters: allParameters);
  }

  static void onMediaStoragePermissionRequested(
      {required String source,
      required bool isGranted,
      bool isAndroid12OrBelow = true,
      bool isOpeningAppSettings = false,
      bool isiOS = false,
      Map<String, dynamic>? parameters}) {
    Map<String, dynamic> allParameters = {
      "source": source,
      "is_granted": isGranted,
      "is_android_12_or_below": isAndroid12OrBelow,
      "is_ios": isiOS,
      "is_opening_app_settings": isOpeningAppSettings,
      "is_permanently_denied": isOpeningAppSettings,
    };
    if (parameters != null) {
      allParameters.addAll(parameters);
    }
    logEvent(
        name: "requested_media_storage_permission", parameters: allParameters);
  }

  static void onPosterLayoutSwipe({
    required String reason,
    required String direction,
    Map<String, dynamic>? parameters,
  }) {
    Map<String, dynamic> allParameters = {
      "direction": direction,
      "reason": reason,
    };
    if (parameters != null) {
      allParameters.addAll(parameters);
    }
    logEvent(name: "poster_layout_swipe", parameters: allParameters);
  }

  static void onPosterDownloadClicked({
    Map<String, dynamic>? parameters,
    required PosterType posterType,
  }) {
    logEvent(name: "poster_download_clicked", parameters: {
      "poster_type": posterType.asMethod(),
      ...?parameters,
    });
  }

  static void onSubscriptionPageCloseClicked({required String orderId}) {
    logEvent(name: "subscription_page_close_clicked", parameters: {
      "order_id": orderId,
    });
  }

  static void onPayClicked(
      {required String orderId,
      required String pageName,
      Map<String, dynamic>? otherParameters}) {
    Map<String, dynamic> parameters = {
      "order_id": orderId,
      "page_name": pageName,
    };
    if (otherParameters != null) {
      parameters.addAll(otherParameters);
    }
    logEvent(name: "pay_clicked", parameters: parameters);
  }

  static void onPaymentLinkShare({
    required String orderId,
    required String pageName,
    Map<String, dynamic>? otherParameters,
  }) {
    Map<String, dynamic> parameters = {
      "order_id": orderId,
      "page_name": pageName,
    };
    if (otherParameters != null) {
      parameters.addAll(otherParameters);
    }
    logEvent(name: "payment_link_share", parameters: parameters);
  }

  static void onPaymentStarted({required String orderId}) {
    logEvent(name: "payment_started", parameters: {
      "order_id": orderId,
    });
  }

  static void onPaymentSucceeded({required String orderId}) {
    logEvent(name: "payment_success_received", parameters: {
      "order_id": orderId,
    });
  }

  static void onPaymentFailed({required String orderId}) {
    logEvent(name: "payment_failure_received", parameters: {
      "order_id": orderId,
    });
  }

  static void onNotificationReceived(
      {required String source,
      required String appState,
      required Map<String, dynamic> props}) {
    logEvent(
        name: "notification_received",
        parameters: {"source": source, "app_state": appState, ...props});
  }

  static void onNotificationPermissionChanged(
      {required String authorizationStatus}) {
    logEvent(
        name: "notification_permission_changed",
        parameters: {"authorization_status": authorizationStatus});
    _mixpanel.registerSuperProperties(
        {"notification_permission_status": authorizationStatus});
  }

  static void onNotificationsMarkAllAsReadClicked(int unreadCount) {
    logEvent(name: "clicked_mark_all_as_read_notifications", parameters: {
      "unread_count": unreadCount,
    });
  }

  static void onNotificationsMarkAllAsReadConfirmed(int unreadCount) {
    logEvent(name: "confirmed_mark_all_as_read_notifications", parameters: {
      "unread_count": unreadCount,
    });
  }

  static void onNotificationsMarkAllAsReadCancelled(int unreadCount) {
    logEvent(name: "cancelled_mark_all_as_read_notifications", parameters: {
      "unread_count": unreadCount,
    });
  }

  //Nav Bar and Update Profile Pic Screen Page Action Events
  static void navigationDrawerOptionTileClickEvent({
    required String option,
    required String source,
  }) {
    Map<String, dynamic> params = {
      "option": option,
      "source": source,
    };
    logEvent(
      name: 'navigation_drawer_option_tile_click',
      parameters: params,
    );
  }

  static void editableProfilePageOpened({required String source}) {
    Map<String, dynamic> params = {
      "source": source,
    };
    logEvent(
      name: "editable_profile_picture_page_opened",
      parameters: params,
    );
  }

  static void deleteProfilePic({
    required String source,
    required String state,
  }) {
    Map<String, dynamic> params = {
      "source": source,
      "state": state,
    };
    logEvent(
      name: "delete_profile_icon_clicked",
      parameters: params,
    );
  }

  static void updateProfilePicture({
    required String source,
    required String mode,
    required String state,
  }) {
    Map<String, dynamic> params = {
      "source": source,
      "mode": mode,
      "state": state,
    };
    logEvent(
      name: "update_profile_picture",
      parameters: params,
    );
  }

  static void logSpeechToTextButtonClick({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_button_click",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextStarted({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_started",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextFinished({
    required String source,
    required String text,
  }) {
    logEvent(
      name: "speech_to_text_finished",
      parameters: {
        'source': source,
        'text': text,
      },
    );
  }

  static void logSpeechToTextCancelled({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_cancelled",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextFailed({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_failed",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextPermissionSheetShow({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_permission_sheet_show",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextPermissionSheetDismiss({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_permission_sheet_dismiss",
      parameters: {
        'source': source,
      },
    );
  }

  static void logSpeechToTextSettingsRedirected({
    required String source,
  }) {
    logEvent(
      name: "speech_to_text_settings_redirected",
      parameters: {
        'source': source,
      },
    );
  }

  static void onClickedJoinChannel(
      {required Map<String, dynamic> params, required String source}) {
    logEvent(
      name: "clicked_join_channel",
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onUploadCreativeClicked(
      {required Map<String, String> params, required String source}) {
    logEvent(
      name: "clicked_upload_creative",
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onInviteToChannelClicked(
      {required Map<String, dynamic> params, required String source}) {
    logEvent(
      name: "clicked_invite_to_channel",
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onProfileEditClick(
      {required String source, Map<String, dynamic> params = const {}}) {
    logEvent(
      name: "click_edit_profile",
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  // Fan Requests Log Events
  static void onFanRequestsBottomSheetShown(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'shown_fan_requests_bottom_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsBottomSheetClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_fan_requests_bottom_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsKnowMoreButtonClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_fan_requests_know_more_button',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsScreenViewed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_fan_requests_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsRequestPremiumSubscriptionClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_request_circle_premium',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestPremiumPosterShown(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'shown_fan_requests_premium_poster',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsRequestPremiumPosterClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_request_fan_poster',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsSheetError(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'fan_requests_sheet_error',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onRetryFanRequestsSheetClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_retry_fan_requests_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsScreenClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_fan_requests_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsScreenError(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'fan_requests_screen_error',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onRetryFanRequestsScreenClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_retry_fan_requests_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsBottomSheetAlreadyShown(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'already_shown_fan_requests_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFanRequestsFeedWidgetViewed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_fan_requests_feed_widget',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onPosterViewed(
      {required Map<String, dynamic> parameters,
      required PosterType posterType,
      required String source}) {
    logEvent(
      name: 'viewed_poster',
      parameters: {
        'poster_type': posterType.asMethod(),
        'source': source,
        ...parameters,
      },
    );
  }

  // Premium Experience Journey Log Events
  static void onPremiumExperienceScreenError(
      {required String errorMessage, required String source}) {
    logEvent(
      name: 'error_premium_experience_screen',
      parameters: {
        'error_message': errorMessage,
        'source': source,
      },
    );
  }

  static void onPremiumExperienceScreenLoaded(
      {required Map<String, dynamic> params}) {
    logEvent(
      name: 'loaded_premium_experience_screen',
      parameters: {
        ...params,
      },
    );
  }

  static void onPremiumExperienceScreenClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_premium_experience_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  //Upcoming Events Feed Item
  static void onUpcomingEventsFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_upcoming_events',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  //Relation Manager Feed Item Log Events
  static void onRelationManagerFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_relation_manager',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onInitiatedWhatsappMessageToRm(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'initiated_whatsapp_message_to_rm',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onInitiatedCallToRm(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'initiated_call_to_rm',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static onFailedCallToRm(
      {required String source,
      Map<String, dynamic>? params,
      required String reason}) {
    params ??= {};
    logEvent(
      name: 'failed_call_to_rm',
      parameters: {
        'source': source,
        'reason': reason,
        ...params,
      },
    );
  }

  //Usage Counts Log Events
  static void onUsageCountsFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_usage_counts',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  //Premium Members Feed Item Log Events
  static void onPremiumMembersFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_premium_members',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  //Profile Views Feed Item & Profile Viewers Screen Log Events
  static void onProfileViewsFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_profile_views',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onProfileViewsSeeMoreClicked(
      {required String source,
      required int index,
      Map<String, dynamic>? params,
      required bool isLocked}) {
    params ??= {};
    logEvent(
      name: 'clicked_see_more_profile_views',
      parameters: {
        'source': source,
        'is_locked': isLocked,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onProfileViewersScreenViewed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_profile_viewers_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  //My Poster Styles Log Events
  static void onMyPosterStylesFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_my_poster_styles',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  //Events Media Carousel Log Events
  static void onEventsMediaCarouselFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_events_media_carousel',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  //Payment Feed Item Log Events
  static void onPaymentFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_payment_item',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onPaymentFeedItemClicked(
      {required String source,
      required int index,
      Map<String, dynamic>? params,
      required String deeplink}) {
    params ??= {};
    logEvent(
      name: 'clicked_payment_item',
      parameters: {
        'source': source,
        'scroll_index': index,
        'deeplink': deeplink,
        ...params,
      },
    );
  }

  //Premium Exp Screen Button Log Events
  static void onPremiumExperienceButtonClicked({Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_premium_experience_button',
      parameters: {
        ...params,
      },
    );
  }

  static void onWaitlistResponseReceived({
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'received_waitlist_response',
      parameters: {
        ...params,
      },
    );
  }

  //Payment Bottom Sheet Logs
  static void onPaymentSheetError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_payment_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onPaymentSheetLoaded({required Map<String, dynamic> params}) {
    logEvent(
      name: 'loaded_payment_sheet',
      parameters: {
        ...params,
      },
    );
  }

  static void onPaymentSheetClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_payment_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onSubscriptionItemSelected({
    Map<String, dynamic>? params,
    required int planId,
    required String source,
    required bool isSelectedDefault,
  }) {
    params ??= {};
    logEvent(
      name: 'selected_subscription_item',
      parameters: {
        'source': source,
        'is_selected_default': isSelectedDefault,
        'plan_id': planId,
        ...params,
      },
    );
  }

  //FAQs Feed Item Log Events
  static void onFaqsFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_faqs_item',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onFaqButtonClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_faq_button',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  //Cancel Membership Sheet
  static void onCancelMembershipSheetLoaded(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'loaded_cancel_membership_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onCancelMembershipSheetError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_cancel_membership_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onCancelMembershipSheetClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_cancel_membership_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onCancelMembershipClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_cancel_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onContinueMembershipClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_continue_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  // Cancel Reasons
  static void onCancelReasonSelected(
      {required String source,
      required String reason,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'selected_cancel_reason',
      parameters: {
        'source': source,
        'reason': reason,
        ...params,
      },
    );
  }

  static void onMembershipCancelled(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'cancelled_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onCancellingMembershipFailed(
      {required String source,
      required String errorMessage,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'failed_cancelling_membership',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onCancelReasonsSheetClosed(
      {required String source, List<String> cancelReasons = const []}) {
    logEvent(
      name: 'closed_cancel_reasons_sheet',
      parameters: {
        'source': source,
        'cancel_reasons': cancelReasons,
      },
    );
  }

  // Cancel Flow Downgrade Sheet Events
  static void onCancelFlowDowngradeSheetViewed({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'viewed_cancel_flow_downgrade_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onCancelFlowDowngradeSheetFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_cancel_flow_downgrade_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onCancelFlowDowngradeSheetClosed({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'closed_cancel_flow_downgrade_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  // Downgrade Membership Events
  static void onDowngradeMembershipClicked({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_downgrade_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onDowngradedMembership({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'downgraded_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onDowngradingMembershipFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_downgrading_membership',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  // Premium Benefits Loss Screen
  static void onPremiumBenefitsLossScreenFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_premium_benefits_loss_screen',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  // Cancel UX Flow Action Button Events
  // Extend Membership
  static void onExtendMembershipClicked({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_extend_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onMembershipExtended({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'extended_membership',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onExtendingMembershipFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_extending_membership',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  // Cancellation Confirmation Sheet Events
  static void onCancellationConfirmationSheetFailed({
    required String errorMessage,
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_cancellation_confirm_sheet',
      parameters: {
        'error_message': errorMessage,
        'source': source,
        ...params,
      },
    );
  }

  static void onCancellationConfirmationSheetClosed({
    required String source,
    required String mode,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'closed_cancellation_confirm_sheet',
      parameters: {
        'source': source,
        'mode': mode,
        ...params,
      },
    );
  }

  //Payment Flow Events
  static void onPremiumPaymentFailed(
      {required String source,
      required int planId,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'failed_premium_payment',
      parameters: {
        'plan_id': planId,
        'source': source,
        ...params,
      },
    );
  }

  static void onPremiumPaymentSucceeded(
      {required String source,
      required int planId,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'succeeded_premium_payment',
      parameters: {
        'plan_id': planId,
        'source': source,
        ...params,
      },
    );
  }

  static void onPremiumPaymentInitiated(
      {required String source,
      required int planId,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'initiated_premium_payment',
      parameters: {
        'plan_id': planId,
        'source': source,
        ...params,
      },
    );
  }

  static void onPayButtonClicked(
      {required String source,
      required int plainId,
      UpiApp? selectedUpiApp,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_pay_button',
      parameters: {
        'plan_id': plainId,
        'source': source,
        'selected_upi_app': selectedUpiApp?.name ?? 'NONE',
        'selected_upi_app_package_name': selectedUpiApp?.packageName,
        ...params,
      },
    );
  }

  static void onPremiumPaymentLinkShare({
    required String source,
    required int planId,
    required String pageName,
    Map<String, dynamic>? otherParameters,
  }) {
    Map<String, dynamic> parameters = {
      "plan_id": planId,
      "page_name": pageName,
      "source": source,
    };
    if (otherParameters != null) {
      parameters.addAll(otherParameters);
    }
    logEvent(name: "premium_payment_link_share", parameters: parameters);
  }

  //Premium Success Screen Log Events
  static void onPremiumSuccessScreenLoaded(
      {required Map<String, dynamic> params}) {
    logEvent(
      name: 'loaded_premium_success_screen',
      parameters: {
        ...params,
      },
    );
  }

  static void onPremiumSuccessScreenError(
      {required String source,
      required String errorMessage,
      required bool isTrialPopup}) {
    logEvent(
      name: 'error_premium_success_screen',
      parameters: {
        'is_trial_popup': isTrialPopup,
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onPremiumSuccessButtonClicked(
      {required String source,
      required bool isTrialPopup,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_premium_success_button',
      parameters: {
        'is_trial_popup': isTrialPopup,
        'source': source,
        ...params,
      },
    );
  }

  //Pay wall Screen Log Events
  static void onPaywallScreenLoaded({required Map<String, dynamic> params}) {
    logEvent(
      name: 'loaded_paywall_screen',
      parameters: {
        ...params,
      },
    );
  }

  static void onAnnualPaywallScreenLoaded(
      {required Map<String, dynamic> params}) {
    logEvent(
      name: 'loaded_annual_paywall_screen',
      parameters: {
        ...params,
      },
    );
  }

  static void onPaywallScreenError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_paywall_screen',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onAnnualPaywallScreenError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_annual_paywall_screen',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onPaywallClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_paywall_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onAnnualPaywallClosed(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'closed_annual_paywall_screen',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onPaywallHelpClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_paywall_help',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  // App Icon Change Events
  static void onAppIconChanged({required String from, required String to}) {
    logEvent(
      name: 'app_icon_changed',
      parameters: {
        'from': from,
        'to': to,
      },
    );
  }

  // Posters Feed View Events
  static void onPostersFeedFetched({
    required String source,
    required int itemsCount,
    required int pageNumber,
    required bool isRefresh,
    required bool isLastPage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'fetched_posters_feed',
      parameters: {
        'source': source,
        'items_count': itemsCount,
        'page_number': pageNumber,
        'is_refresh': isRefresh,
        'is_last_page': isLastPage,
        ...params,
      },
    );
  }

  static void onPostersFeedFailed({
    required String source,
    required int pageNumber,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_posters_feed',
      parameters: {
        'source': source,
        'page_number': pageNumber,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onPostersFeedFilterSelected({
    required String source,
    Map<String, dynamic>? params,
    bool isScrollingToTop = false,
  }) {
    params ??= {};
    logEvent(
      name: 'selected_posters_feed_filter',
      parameters: {
        'source': source,
        'is_scrolling_to_top': isScrollingToTop,
        ...params,
      },
    );
  }

  static void onFiltersFetched({
    required String source,
    required int filtersCount,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'fetched_filters',
      parameters: {
        'source': source,
        'filters_count': filtersCount,
        ...params,
      },
    );
  }

  static void onFiltersEmptyAreaTapped({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'tapped_filters_empty_area',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onFiltersFetchFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_fetch_filters',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onSeeMoreCreativesClicked({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_see_more_creatives',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onPosterHelpTutorialShown({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'shown_poster_help_tutorial',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onPosterHelpTutorialCompleted({
    required String source,
    bool autoSkip = false,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'completed_poster_help_tutorial',
      parameters: {
        'source': source,
        'auto_skip': autoSkip,
        ...params,
      },
    );
  }

  static void onMenuDrawerOpened() {
    logEvent(name: 'opened_menu_drawer');
  }

  // Juspay
  static void failedPaymentJuspay({
    Map<String, dynamic>? errorAnalyticsParams,
  }) {
    logEvent(
      name: 'failed_payment_juspay',
      parameters: {
        ...errorAnalyticsParams ?? {},
      },
    );
  }

  static void onJuspayInitializationFailed({
    Map<String, dynamic>? errorAnalyticsParams,
  }) {
    logEvent(
      name: 'failed_juspay_initialization',
      parameters: {
        ...errorAnalyticsParams ?? {},
      },
    );
  }

  static void onPreferredUpiAppsParseFailed({
    Map<String, dynamic>? errorAnalyticsParams,
  }) {
    logEvent(
      name: 'failed_parsing_preferred_upi_apps',
      parameters: {
        ...errorAnalyticsParams ?? {},
      },
    );
  }

  // Poster User Photo Update Events
  static void onPosterPhotoUpdateClicked({
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_update_poster_photo',
      parameters: {
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void onPhotoPhotoUpdateGalleryIconClicked({
    required String source,
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_poster_photo_update_gallery',
      parameters: {
        'source': source,
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void onPosterPhotoSelectedFromGallery({
    required String source,
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'selected_poster_photo_from_gallery',
      parameters: {
        'source': source,
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void onPosterPhotoBgRemoved({
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'removed_poster_photo_bg',
      parameters: {
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void onPosterPhotoRemoveBgFailed({
    required String userPhotoType,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_poster_photo_remove_bg',
      parameters: {
        'user_photo_type': userPhotoType,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onPosterPhotoUpdated({
    required String mode,
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'updated_poster_photo',
      parameters: {
        'mode': mode,
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void failedToUpdatePosterPhoto({
    required String mode,
    required String userPhotoType,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_update_poster_photo',
      parameters: {
        'mode': mode,
        'user_photo_type': userPhotoType,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onPosterPhotosHistoryLoaded({
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'loaded_poster_photos_history',
      parameters: {
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  static void onPosterPhotosHistoryLoadFailed({
    required String userPhotoType,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_load_poster_photos_history',
      parameters: {
        'user_photo_type': userPhotoType,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onUserPhotoTappedForCameraIcon({
    required String userPhotoType,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'tapped_user_photo_for_camera_icon',
      parameters: {
        'user_photo_type': userPhotoType,
        ...params,
      },
    );
  }

  // Support Flow
  static void onSupportClicked({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_support',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onSupportSubmitError({
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'error_support_submit',
      parameters: {
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onSupportOptionClicked({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'clicked_support_option',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onSupportSheetFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_support_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  //Downgrade Sheet Events
  static void onDowngradeSheetLoaded(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'loaded_downgrade_sheet',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onDowngradeSheetError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_downgrade_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onDowngradeConsentAccepted({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'accepted_downgrade_consent',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onDowngradeConsentRejected({
    required String source,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'rejected_downgrade_consent',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onDowngradeFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_downgrade',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  // Offer Reveal Sheet
  static void onOfferRevealSheetError(
      {required String source, required String errorMessage}) {
    logEvent(
      name: 'error_offer_reveal_sheet',
      parameters: {
        'source': source,
        'error_message': errorMessage,
      },
    );
  }

  static void onSkippedRevealOffer(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'skipped_reveal_offer',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onRevealOfferClicked(
      {required String source, Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_reveal_offer',
      parameters: {
        'source': source,
        ...params,
      },
    );
  }

  static void onOfferRevealStatusFailed({
    required String source,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_offer_reveal_status',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  //Offer Feed item
  static void onOfferFeedItemViewed(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'viewed_offer_feed_item',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onOfferFeedItemClicked(
      {required String source,
      required int index,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'clicked_offer_feed_item',
      parameters: {
        'source': source,
        'scroll_index': index,
        ...params,
      },
    );
  }

  static void onUpgradeFeedItemViewed(
      {required UpgradeFeedItem item,
      required String source,
      required int scrollIndex}) {
    logEvent(name: 'viewed_upgrade_feed_item', parameters: {
      ...item.analyticsParams,
      "source": source,
      "scroll_index": scrollIndex
    });
  }

  // Layout Feedback Review
  static void onLayoutFeedbackReviewSubmitted(
      {required String source,
      required String reviewStatus,
      Map<String, dynamic>? params}) {
    params ??= {};
    logEvent(
      name: 'submitted_layout_feedback_review',
      parameters: {
        'source': source,
        'review_status': reviewStatus,
        ...params,
      },
    );
  }

  static void onLayoutFeedbackReviewFailed({
    required String source,
    required String reviewStatus,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_layout_feedback_review',
      parameters: {
        'source': source,
        'error_message': errorMessage,
        'review_status': reviewStatus,
        ...params,
      },
    );
  }

  //Verification Explainer Sheet
  static void onVerificationExplainerSheetClicked({
    required int badgeId,
    required String roleDescription,
    required String verificationStatus,
    required String source,
  }) {
    logEvent(name: 'clicked_verification_explainer_sheet', parameters: {
      "badge_id": badgeId,
      "role_description": roleDescription,
      "verification_status": verificationStatus,
      "source": source
    });
  }

  static void onVerificationExplainerSheetClosed({
    required int badgeId,
    required String roleDescription,
    required String verificationStatus,
    required String source,
  }) {
    logEvent(name: 'closed_verification_explainer_sheet', parameters: {
      "badge_id": badgeId,
      "role_description": roleDescription,
      "verification_status": verificationStatus,
      "source": source
    });
  }

  // Video Poster Analytics Events
  static void onVideoPosterGenerationStarted({
    required String method,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'started_video_poster_generation',
      parameters: {
        'method': method,
        ...params,
      },
    );
  }

  static void onVideoPosterGenerationFailed({
    required String method,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_video_poster_generation',
      parameters: {
        'method': method,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onVideoPosterDownloadFailed({
    required String method,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_video_poster_download',
      parameters: {
        'method': method,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onVideoPosterShareFailed({
    required String method,
    required String errorMessage,
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'failed_video_poster_share',
      parameters: {
        'method': method,
        'error_message': errorMessage,
        ...params,
      },
    );
  }

  static void onVideoPosterDownloadCompleted({
    Map<String, dynamic>? params,
  }) {
    params ??= {};
    logEvent(
      name: 'completed_video_poster_download',
      parameters: params,
    );
  }
}
